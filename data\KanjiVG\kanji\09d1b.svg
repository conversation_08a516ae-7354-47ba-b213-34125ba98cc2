<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09d1b" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09d1b" kvg:element="鴛">
	<g id="kvg:09d1b-g1" kvg:element="夗" kvg:position="top">
		<g id="kvg:09d1b-g2" kvg:element="夕" kvg:position="left">
			<path id="kvg:09d1b-s1" kvg:type="㇒" d="M35.02,8.64c0,1.11-0.34,2.61-1.01,3.64c-2.99,5.25-7.64,11.94-15.16,18.71"/>
			<path id="kvg:09d1b-s2" kvg:type="㇇" d="M33.8,18.92c1.56-0.01,3.1-0.21,4.59-0.59c2.15-0.52,4.76-1.21,6.89-1.66c2.61-0.55,4.29,0.02,3.04,2.81c-3.95,8.85-18.28,26.75-33.08,31.02"/>
			<path id="kvg:09d1b-s3" kvg:type="㇔" d="M27.25,27.38c2.45,1.55,5.43,5.24,6.04,7.65"/>
		</g>
		<g id="kvg:09d1b-g3" kvg:element="卩" kvg:position="right">
			<path id="kvg:09d1b-s4" kvg:type="㇆" d="M58.75,15.38c1.12,0.62,2.88,1,5.35,0.63c3.44-0.51,14.57-2.53,16.23-2.82c3.16-0.56,4.38,1.44,3.8,4.08c-0.77,3.52-3.28,9.55-5.67,14.48c-1.85,3.81-4.54,0.42-5,0.25"/>
			<path id="kvg:09d1b-s5" kvg:type="㇟/㇑" d="M60.74,16.73c0.58,0.58,0.75,1.72,0.75,2.94c0,7.28-0.06,9.51-0.06,13.22c0,7.44,1.62,8.72,15.8,8.67C94,41.5,94.39,38.93,94.39,33.49"/>
		</g>
	</g>
	<g id="kvg:09d1b-g4" kvg:element="鳥" kvg:position="bottom" kvg:radical="general">
		<path id="kvg:09d1b-s6" kvg:type="㇒" d="M51.73,38.18c0.06,0.58-0.03,1.13-0.26,1.65c-0.69,1.83-2.42,4.07-5.84,7.05"/>
		<path id="kvg:09d1b-s7" kvg:type="㇑" d="M34.63,48.79c0.91,0.91,0.91,2.42,0.82,3.71c0,3.65-0.1,10.93-0.17,17.62c-0.03,3.16-0.06,6.19-0.06,8.64"/>
		<path id="kvg:09d1b-s8" kvg:type="㇕a" d="M36.02,50.31c2.13-0.06,34.09-2.8,36.17-2.8c3.07,0,3.32,1.24,2.79,3.81c-0.14,0.71-0.98,6.44-2.23,11.77"/>
		<path id="kvg:09d1b-s9" kvg:type="㇐a" d="M36.36,57.08c2.61,0,33.42-2.57,36.37-2.57"/>
		<path id="kvg:09d1b-s10" kvg:type="㇐a" d="M36.24,64.09c5.31-0.25,28.87-1.99,35.48-2.2"/>
		<path id="kvg:09d1b-s11" kvg:type="㇐b" d="M36.53,71.22c9.84-0.76,35.06-2.9,44.34-3.67c2.58-0.26,4.75-0.3,7.75-0.23"/>
		<path id="kvg:09d1b-s12" kvg:type="㇆a" d="M35.98,78.9c19.27-2.03,31.64-2.78,48.02-3.47c5-0.21,5.75,1.73,4.5,5.7c-1.79,5.66-4.54,12.05-7.5,16.4c-2.91,4.27-5.5,1.48-7.5-0.83"/>
		<g id="kvg:09d1b-g5" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09d1b-s13" kvg:type="㇔" d="M25.57,85.49C25,91.62,24,95.25,21.33,98.75"/>
			<path id="kvg:09d1b-s14" kvg:type="㇔" d="M35.75,85.83c1.89,1.87,3.69,7,4.16,9.9"/>
			<path id="kvg:09d1b-s15" kvg:type="㇔" d="M50,83.76c1.5,1.82,3.87,7.47,4.25,10.3"/>
			<path id="kvg:09d1b-s16" kvg:type="㇔" d="M63.25,82.03c1.76,1.51,4.56,6.2,5,8.54"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09d1b" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 27.50 8.50)">1</text>
	<text transform="matrix(1 0 0 1 40.50 14.50)">2</text>
	<text transform="matrix(1 0 0 1 23.25 36.48)">3</text>
	<text transform="matrix(1 0 0 1 62.50 13.50)">4</text>
	<text transform="matrix(1 0 0 1 54.50 23.50)">5</text>
	<text transform="matrix(1 0 0 1 43.50 38.50)">6</text>
	<text transform="matrix(1 0 0 1 27.50 53.50)">7</text>
	<text transform="matrix(1 0 0 1 38.50 47.50)">8</text>
	<text transform="matrix(1 0 0 1 40.50 55.50)">9</text>
	<text transform="matrix(1 0 0 1 38.69 62.95)">10</text>
	<text transform="matrix(1 0 0 1 38.91 69.50)">11</text>
	<text transform="matrix(1 0 0 1 38.75 76.50)">12</text>
	<text transform="matrix(1 0 0 1 15.50 85.50)">13</text>
	<text transform="matrix(1 0 0 1 28.25 92.50)">14</text>
	<text transform="matrix(1 0 0 1 41.50 91.50)">15</text>
	<text transform="matrix(1 0 0 1 54.50 87.50)">16</text>
</g>
</svg>
