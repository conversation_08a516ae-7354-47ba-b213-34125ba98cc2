<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09f9c" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09f9c" kvg:element="龜" kvg:radical="general">
	<g id="kvg:09f9c-g1" kvg:element="𠂊" kvg:variant="true" kvg:original="勹">
		<path id="kvg:09f9c-s1" kvg:type="㇒" d="M42,11c0.07,0.45,0.14,1.16-0.13,1.8C40.3,16.59,31.31,24.91,19,30"/>
		<path id="kvg:09f9c-s2" kvg:type="㇇" d="M39,17.53c4.75-0.28,18.62-1.67,22.94-3.29c3.31-1.24,4.81,0.26,2.31,3.13C62.04,19.91,60,22,56,26.75"/>
	</g>
	<path id="kvg:09f9c-s3" kvg:type="㇑" d="M26.22,29.05c0.62,0.62,0.71,1.02,0.98,1.83c0.93,2.79,1.55,7.12,1.99,10.97"/>
	<path id="kvg:09f9c-s4" kvg:type="㇕" d="M27.46,30.48c8.64-1.28,46.5-4.52,51.98-5.37c2.01-0.31,3.23,0.82,2.94,1.91c-1.18,4.41-1.88,5.41-2.71,8.51"/>
	<path id="kvg:09f9c-s5" kvg:type="㇐" d="M30.47,39.98c2.58,0,15.95-1.56,18.06-1.56"/>
	<path id="kvg:09f9c-s6" kvg:type="㇐" d="M60.57,37.87c4.69-0.44,18.53-2.11,21.34-2.11"/>
	<path id="kvg:09f9c-s7" kvg:type="㇑" d="M60.41,27.65c0.2,0.89,0,58.44-0.2,63.99"/>
	<path id="kvg:09f9c-s8" kvg:type="㇟" d="M49.34,28.58c0,11.07-0.03,51.45-0.03,56.4C49.31,96.53,52.05,97,71.98,97c20.87,0,22.41-1.58,22.41-9.89"/>
	<g id="kvg:09f9c-g2" kvg:element="⺕" kvg:variant="true" kvg:original="彑">
		<path id="kvg:09f9c-s9" kvg:type="㇕" d="M20.94,47.92c1.56,0.58,1.76,1.04,3.14,0.8c5.66-0.97,8.16-1.72,15.01-2.47c1.15-0.13,2.34,1.26,2.04,2.78c-0.79,3.97-1.37,5.42-2.43,12.25"/>
		<path id="kvg:09f9c-s10" kvg:type="㇕" d="M16.69,55.17c1.56,0.58,1.75,0.9,3.14,0.8c8.5-0.61,57.83-7.47,64.51-7.47c1.97,0,3,1.73,3.04,3.28c0.12,4.72-1.87,21.92-2.93,28.75"/>
		<path id="kvg:09f9c-s11" kvg:type="㇐" d="M21.36,63.37c0.76,0.09,2.14,0.4,3.04,0.28c10.61-1.4,14.92-2.39,15.83-2.3"/>
	</g>
	<g id="kvg:09f9c-g3" kvg:element="⺕" kvg:variant="true" kvg:original="彑">
		<path id="kvg:09f9c-s12" kvg:type="㇕" d="M19.94,72.92c1.52,0.15,2.26,0.48,3.64,0.3C29.25,72.5,33.5,71,39.1,70.25c1.96-0.26,2.34,0.76,2.04,2.28c-0.79,3.97-1.37,10.42-2.43,17.25"/>
		<path id="kvg:09f9c-s13" kvg:type="㇐c" d="M15.92,81.58c1.33,0.42,3.44,0.94,4.95,0.8c13.21-1.21,51.39-4.63,64.18-5.8"/>
		<path id="kvg:09f9c-s14" kvg:type="㇐c" d="M19.86,91.37c0.76,0.09,2.13,0.33,3.04,0.28c6.36-0.4,14.36-1.15,17.45-1.39"/>
	</g>
	<path id="kvg:09f9c-s15" kvg:type="㇒" d="M78.02,53.64c0.03,0.45,0.07,1.16-0.07,1.8c-0.8,3.81-5.38,12.16-11.65,17.27"/>
	<path id="kvg:09f9c-s16" kvg:type="㇔" d="M67.02,58.58C71.27,60.9,78,68.11,79.06,71.72"/>
</g>
</g>
<g id="kvg:StrokeNumbers_09f9c" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 32.25 12.13)">1</text>
	<text transform="matrix(1 0 0 1 48.75 13.55)">2</text>
	<text transform="matrix(1 0 0 1 20.25 39.13)">3</text>
	<text transform="matrix(1 0 0 1 35.50 27.13)">4</text>
	<text transform="matrix(1 0 0 1 32.25 37.63)">5</text>
	<text transform="matrix(1 0 0 1 63.50 34.63)">6</text>
	<text transform="matrix(1 0 0 1 54.50 35.50)">7</text>
	<text transform="matrix(1 0 0 1 43.50 35.50)">8</text>
	<text transform="matrix(1 0 0 1 14.50 48.50)">9</text>
	<text transform="matrix(1 0 0 1 6.50 56.50)">10</text>
	<text transform="matrix(1 0 0 1 12.50 64.50)">11</text>
	<text transform="matrix(1 0 0 1 11.50 73.50)">12</text>
	<text transform="matrix(1 0 0 1 6.50 82.50)">13</text>
	<text transform="matrix(1 0 0 1 10.50 92.50)">14</text>
	<text transform="matrix(1 0 0 1 68.50 57.38)">15</text>
	<text transform="matrix(1 0 0 1 62.00 66.00)">16</text>
</g>
</svg>
