<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09470" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09470" kvg:element="鑰">
	<g id="kvg:09470-g1" kvg:element="金" kvg:position="left" kvg:radical="general">
		<path id="kvg:09470-s1" kvg:type="㇒" d="M27.75,14.24c0,0.79,0.05,1.42-0.15,2.37c-1.08,4.94-11.34,21.44-20.12,28.76"/>
		<path id="kvg:09470-s2" kvg:type="㇔/㇏" d="M29.89,19.96C34,21.83,39.25,26.8,41.25,31.5"/>
		<path id="kvg:09470-s3" kvg:type="㇐" d="M17.5,40.78c1.6,0,2.32,0.11,2.84,0.07c4.61-0.37,9.94-1.82,15.37-2.08c0.79-0.04,0.95-0.06,2.29-0.06"/>
		<path id="kvg:09470-s4" kvg:type="㇐" d="M11.59,55.31c0.59,0.33,2.56,0.48,3.17,0.43c5.49-0.49,16.49-2.49,23.28-3.17c0.74-0.07,2.38-0.13,3.49,0.15"/>
		<path id="kvg:09470-s5" kvg:type="㇑a" d="M25.95,41.69c1.24,0.78,1.24,2.52,1.24,3.14c0,4.35,0.62,35.13,0.31,39.48"/>
		<path id="kvg:09470-s6" kvg:type="㇔" d="M13.89,65.51c3.49,5.13,5.19,10.69,5.9,13.69"/>
		<path id="kvg:09470-s7" kvg:type="㇒" d="M38.76,59.55c0.26,0.87,0.39,2.13,0.29,2.87c-0.26,1.88-2.27,5.33-5.13,10.86"/>
		<path id="kvg:09470-s8" kvg:type="㇀/㇐" d="M11,89.32c1.26,0.9,2.77,0.67,4.54,0.22C16.84,89.21,26.89,84.38,40,79"/>
	</g>
	<g id="kvg:09470-g2" kvg:element="龠" kvg:position="right">
		<g id="kvg:09470-g3" kvg:element="人" kvg:position="top">
			<g id="kvg:09470-g4" kvg:element="丿">
				<path id="kvg:09470-s9" kvg:type="㇒" d="M68.85,10.25c0.08,0.63,0.3,1.72-0.15,2.55C65.43,18.84,55.91,29.27,41,37.25"/>
			</g>
			<path id="kvg:09470-s10" kvg:type="㇏" d="M68.95,12.38c3.71,3.54,19.65,15.46,23.75,18.05c1.39,0.88,3.17,1.26,4.56,1.51"/>
		</g>
		<g id="kvg:09470-g5" kvg:position="bottom">
			<path id="kvg:09470-s11" kvg:type="㇐" d="M56.15,30.69c0.67,0.33,1.9,0.37,2.57,0.33c4.82-0.27,14.77-1.52,20.22-1.88c1.12-0.07,1.79,0.16,2.35,0.32"/>
			<g id="kvg:09470-g6" kvg:element="口">
				<path id="kvg:09470-s12" kvg:type="㇑" d="M45.76,39.83c0.22,0.28,0.48,0.51,0.55,0.88c0.63,3.25,1.44,8.58,2.07,13.35"/>
				<path id="kvg:09470-s13" kvg:type="㇕b" d="M47.15,41.24c4.1-0.69,6.64-1.24,10.42-1.93c0.95-0.17,1.52,0.8,1.39,1.59c-0.57,3.28-1.06,4.8-2.06,10.26"/>
				<path id="kvg:09470-s14" kvg:type="㇐b" d="M48.85,53.35c3.06-0.42,5.4-0.6,9.5-0.78"/>
			</g>
			<g id="kvg:09470-g7" kvg:element="口">
				<path id="kvg:09470-s15" kvg:type="㇑" d="M63.95,38.55c0.2,0.27,0.43,0.48,0.5,0.84c0.58,3.11,1.31,9.64,1.89,14.21"/>
				<path id="kvg:09470-s16" kvg:type="㇕b" d="M64.72,39.87c3.75-0.66,6.97-0.68,10.43-1.35c0.87-0.16,1.39,0.77,1.27,1.52c-0.52,3.14-0.97,4.6-1.88,9.82"/>
				<path id="kvg:09470-s17" kvg:type="㇐b" d="M66.77,51.96c3.73-0.21,5.23-0.71,9.14-0.71"/>
			</g>
			<g id="kvg:09470-g8" kvg:element="口">
				<path id="kvg:09470-s18" kvg:type="㇑" d="M81.61,38.05c0.22,0.27,0.46,0.48,0.53,0.84c0.61,3.11,1.4,9.64,2.01,14.2"/>
				<path id="kvg:09470-s19" kvg:type="㇕b" d="M82.46,39.87c3.99-0.66,6.41-1.18,10.09-1.85c0.92-0.16,1.48,0.77,1.35,1.52c-0.55,3.14-1.03,4.6-2,9.82"/>
				<path id="kvg:09470-s20" kvg:type="㇐b" d="M84.61,51.46c3.14-0.21,4.63-0.09,8.71-0.71"/>
			</g>
			<g id="kvg:09470-g9" kvg:element="冊">
				<g id="kvg:09470-g10" kvg:element="冂">
					<path id="kvg:09470-s21" kvg:type="㇑" d="M47.67,62.1c0.4,0.7,0.89,1.8,0.95,3.04c0.33,6.69-0.39,26.9,0.08,33.7"/>
					<path id="kvg:09470-s22" kvg:type="㇆a" d="M48.29,63.17c5.13-0.3,39.54-3.75,40.56-3.75c1.9,0,3.44,2.08,3.44,4.36c0,7.28,0.06,16.15-0.03,29.12c-0.06,8.57-3.7,4.16-6.42,1.03"/>
				</g>
				<g id="kvg:09470-g11" kvg:element="廾" kvg:variant="true">
					<g id="kvg:09470-g12" kvg:element="丿" kvg:variant="true">
						<path id="kvg:09470-s23" kvg:type="㇐" d="M48.6,77.05c10.44-0.37,32.54-2.61,43.51-2.6"/>
					</g>
					<g id="kvg:09470-g13" kvg:element="十" kvg:variant="true">
						<path id="kvg:09470-s24" kvg:type="㇑" d="M62.23,63.91c0.59,0.88,1.02-0.64,1.05,2.19c0.15,13.61-0.2,26.76-0.23,31.38"/>
						<path id="kvg:09470-s25" kvg:type="㇑" d="M75.41,61.88c0.59,0.88,1.01,0.89,1.04,3.72c0.15,13.6-0.2,26.88-0.23,31.5"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09470" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 20.85 13.50)">1</text>
	<text transform="matrix(1 0 0 1 33.50 20.50)">2</text>
	<text transform="matrix(1 0 0 1 21.50 38.50)">3</text>
	<text transform="matrix(1 0 0 1 5.50 56.85)">4</text>
	<text transform="matrix(1 0 0 1 20.50 49.50)">5</text>
	<text transform="matrix(1 0 0 1 6.58 65.50)">6</text>
	<text transform="matrix(1 0 0 1 31.50 62.50)">7</text>
	<text transform="matrix(1 0 0 1 3.50 91.50)">8</text>
	<text transform="matrix(1 0 0 1 60.50 11.03)">9</text>
	<text transform="matrix(1 0 0 1 74.50 16.50)">10</text>
	<text transform="matrix(1 0 0 1 62.50 28.50)">11</text>
	<text transform="matrix(1 0 0 1 37.50 47.50)">12</text>
	<text transform="matrix(1 0 0 1 48.50 39.50)">13</text>
	<text transform="matrix(1 0 0 1 48.50 51.50)">14</text>
	<text transform="matrix(1 0 0 1 58.50 50.50)">15</text>
	<text transform="matrix(1 0 0 1 65.50 37.50)">16</text>
	<text transform="matrix(1 0 0 1 67.50 50.50)">17</text>
	<text transform="matrix(1 0 0 1 76.25 49.50)">18</text>
	<text transform="matrix(1 0 0 1 82.50 36.50)">19</text>
	<text transform="matrix(1 0 0 1 84.50 49.50)">20</text>
	<text transform="matrix(1 0 0 1 40.50 69.50)">21</text>
	<text transform="matrix(1 0 0 1 48.50 60.50)">22</text>
	<text transform="matrix(1 0 0 1 51.50 74.50)">23</text>
	<text transform="matrix(1 0 0 1 66.50 69.13)">24</text>
	<text transform="matrix(1 0 0 1 78.75 68.13)">25</text>
</g>
</svg>
