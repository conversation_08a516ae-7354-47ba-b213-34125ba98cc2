<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0961d" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0961d" kvg:element="阝" kvg:original="阜" kvg:radical="general">
	<path id="kvg:0961d-s1" kvg:type="㇇" d="M39.79,13.54c1.56,0.28,3.41,0.8,5.34,0.48c9-1.51,17.37-3.14,20.62-4.18c3.48-1.12,5.32,1.83,3.9,4.59c-2.03,3.95-8.11,15.24-10.81,19.08"/>
	<path id="kvg:0961d-s2" kvg:type="㇁" d="M58.75,33.75c16,11,14.38,37-0.83,27.75"/>
	<path id="kvg:0961d-s3" kvg:type="㇑" d="M41.62,14.5c0.75,0.75,0.96,1.62,0.96,2.75c0,0.85,0.02,51.18,0.03,72.38c0,4.28,0,7.37,0,8.62"/>
</g>
</g>
<g id="kvg:StrokeNumbers_0961d" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 34.25 11.25)">1</text>
	<text transform="matrix(1 0 0 1 52.25 38.25)">2</text>
	<text transform="matrix(1 0 0 1 35.25 23.25)">3</text>
</g>
</svg>
