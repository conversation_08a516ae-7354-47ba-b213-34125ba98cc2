<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09955" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09955" kvg:element="饕">
	<g id="kvg:09955-g1" kvg:element="號" kvg:position="top">
		<g id="kvg:09955-g2" kvg:element="号" kvg:position="left">
			<g id="kvg:09955-g3" kvg:element="口" kvg:position="top">
				<path id="kvg:09955-s1" kvg:type="㇑" d="M18.77,10.88c0.24,0.19,0.49,0.35,0.6,0.59c0.84,1.89,1.85,7.09,2.43,10.48"/>
				<path id="kvg:09955-s2" kvg:type="㇕b" d="M20.28,11.53c5.96-0.79,17.2-1.83,20.27-2.1c1.12-0.1,1.8,0.98,1.64,1.56c-0.67,2.38-1.43,3.92-2.85,7.35"/>
				<path id="kvg:09955-s3" kvg:type="㇐b" d="M22.04,20.2c3.61-0.3,13.84-1.27,18.43-1.57"/>
			</g>
			<g id="kvg:09955-g4" kvg:position="bottom">
				<g id="kvg:09955-g5" kvg:element="一">
					<path id="kvg:09955-s4" kvg:type="㇐" d="M12.5,28.46c0.87,0.27,2.47,0.35,3.34,0.27c7.99-0.75,19.22-2.76,28.79-3.13c1.45-0.06,2.32,0.13,3.05,0.27"/>
				</g>
				<path id="kvg:09955-s5" kvg:type="㇉" d="M25.57,29.61c0.57,0.5,0.72,0.75,0,1.51c-0.72,0.75-3.01,4.27-3.58,5.14c-0.57,0.88,0,2.13,1.15,2.01c1.15-0.13,11.88-2.46,13.2-2.76c2.15-0.5,3.27,0.39,2.87,2.26c-1.15,5.27-5.33,11.61-7.46,14.18c-3.01,3.64-6.24,0.8-8.03-1.25"/>
			</g>
		</g>
		<g id="kvg:09955-g6" kvg:element="虎" kvg:position="right">
			<g id="kvg:09955-g7" kvg:element="虍" kvg:position="tare">
				<path id="kvg:09955-s6" kvg:type="㇑a" d="M68.85,7.58c0.67,0.46,1.2,1,1.2,2.52c0,2.96-0.01,4.11-0.01,6.6"/>
				<path id="kvg:09955-s7" kvg:type="㇐b" d="M70.13,11.2c4.6-0.39,9.79-0.89,14.45-1.2c1.53-0.1,2.47,0.09,3.25,0.19"/>
				<path id="kvg:09955-s8" kvg:type="㇒" d="M56.61,17.68c0.3,0.23,0.42,1.61,0.33,2.45c-0.93,8.77-1.19,16.12-7.7,23.21"/>
				<path id="kvg:09955-s9" kvg:type="㇖a" d="M57.26,19.13c0.71,0.13,2,0.16,3.17,0.03c8.07-0.9,21.97-3.05,29.03-3.16c7.44-0.12,1.58,4.48-0.28,5.91"/>
				<g id="kvg:09955-g8" kvg:element="七">
					<g id="kvg:09955-g9" kvg:element="一">
						<path id="kvg:09955-s10" kvg:type="㇐" d="M61.47,25.71c0.54,0.18,1.54,0.2,2.08,0.18c4.7-0.15,12.55-2.05,18.41-2.5c0.9-0.07,1.44,0.09,1.9,0.18"/>
					</g>
					<path id="kvg:09955-s11" kvg:type="㇟a" d="M69.65,19.42c0.43,0.22,1,1.11,1,1.43c0,2.5-0.08,5.34-0.08,6.46c0,3.32,2,3.07,8.15,3.07c2.29,0,7.23-0.5,9.2-0.95"/>
				</g>
			</g>
			<g id="kvg:09955-g10" kvg:element="儿" kvg:original="八" kvg:position="tarec">
				<g id="kvg:09955-g11" kvg:element="丿" kvg:position="left">
					<path id="kvg:09955-s12" kvg:type="㇒" d="M68.3,34.76c0.02,0.29-0.01,0.75-0.04,1.17c-0.26,3.82-2.51,8.82-6.84,11.24"/>
				</g>
				<g id="kvg:09955-g12" kvg:position="right">
					<path id="kvg:09955-s13" kvg:type="㇟" d="M76,34.77c0.4,0.51,0.73,1.11,0.75,1.9c0.11,3.51,0.21,3.42,0.21,5.81c0,3.77,1.04,4.95,7.66,4.95c8.14,0,9.82-1.18,9.82-4.92"/>
				</g>
			</g>
		</g>
	</g>
	<g id="kvg:09955-g13" kvg:element="食" kvg:variant="true" kvg:position="bottom" kvg:radical="general">
		<path id="kvg:09955-s14" kvg:type="㇒" d="M52.99,45.25c0.11,0.63-0.04,1.68-0.73,2.54C46.59,54.83,30.7,66,13,72.07"/>
		<path id="kvg:09955-s15" kvg:type="㇏" d="M52.75,47.51c4.84,3.43,25.71,14.61,31.05,17.12c1.81,0.85,4.14,1.22,5.95,1.46"/>
		<path id="kvg:09955-s16" kvg:type="㇐" d="M42.84,61.74c0.9,0.16,2,0.34,2.73,0.29c4.09-0.27,11.82-1.76,16.33-2.18c1.18-0.11,1.9,0.14,2.49,0.28"/>
		<path id="kvg:09955-s17" kvg:type="㇕" d="M38.19,69.6c2.55-0.13,27.67-3.16,30-3.31c1.94-0.13,3.18,1.44,3.03,2.21c-0.3,1.56-2.47,10-3.11,13.74"/>
		<path id="kvg:09955-s18" kvg:type="㇐" d="M38.57,76.43c3.48,0,27.09-2.88,31.03-2.88"/>
		<path id="kvg:09955-s19" kvg:type="㇐" d="M38.41,83.95C45.5,83.67,59.25,82,67.6,81.19"/>
		<path id="kvg:09955-s20" kvg:type="㇙" d="M36.5,68.33c0.75,0.92,1.25,1.56,1.25,2.31C37.75,76,38,95.71,38,97.49c0,1.76,0.41,3.96,3.25,2.38c4.25-2.37,9.5-5.69,12-7.28"/>
		<path id="kvg:09955-s21" kvg:type="㇒" d="M76.57,82.45c0.34,1.07,0.05,2.23-0.82,2.85c-2.75,1.95-5.95,4.62-9,6.12"/>
		<path id="kvg:09955-s22" kvg:type="㇏" d="M51.5,86.42c3.53,0,21.16,9.99,23.9,11.2c3.1,1.37,5.77,2.22,7.85,2.49"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09955" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 13.50 18.50)">1</text>
	<text transform="matrix(1 0 0 1 22.50 8.50)">2</text>
	<text transform="matrix(1 0 0 1 24.50 17.50)">3</text>
	<text transform="matrix(1 0 0 1 5.50 28.40)">4</text>
	<text transform="matrix(1 0 0 1 15.50 37.50)">5</text>
	<text transform="matrix(1 0 0 1 61.50 8.50)">6</text>
	<text transform="matrix(1 0 0 1 73.50 8.50)">7</text>
	<text transform="matrix(1 0 0 1 49.50 22.50)">8</text>
	<text transform="matrix(1 0 0 1 58.50 16.50)">9</text>
	<text transform="matrix(1 0 0 1 59.50 24.95)">10</text>
	<text transform="matrix(1 0 0 1 73.50 22.88)">11</text>
	<text transform="matrix(1 0 0 1 57.50 37.50)">12</text>
	<text transform="matrix(1 0 0 1 68.25 43.50)">13</text>
	<text transform="matrix(1 0 0 1 40.25 49.50)">14</text>
	<text transform="matrix(1 0 0 1 64.50 53.50)">15</text>
	<text transform="matrix(1 0 0 1 48.50 58.50)">16</text>
	<text transform="matrix(1 0 0 1 38.50 67.50)">17</text>
	<text transform="matrix(1 0 0 1 41.50 74.50)">18</text>
	<text transform="matrix(1 0 0 1 41.50 81.50)">19</text>
	<text transform="matrix(1 0 0 1 27.50 74.50)">20</text>
	<text transform="matrix(1 0 0 1 74.50 79.50)">21</text>
	<text transform="matrix(1 0 0 1 44.50 90.13)">22</text>
</g>
</svg>
