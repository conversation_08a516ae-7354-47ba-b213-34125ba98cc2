<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09707" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09707" kvg:element="震">
	<g id="kvg:09707-g1" kvg:element="雨" kvg:variant="true" kvg:position="top" kvg:radical="general">
		<path id="kvg:09707-s1" kvg:type="㇐" d="M35.91,14.34c2.52,0.1,4.72,0.07,7.23-0.28c6.06-0.84,16.82-2.23,22.98-2.67c2.44-0.18,5.02-0.4,7.45-0.05"/>
		<path id="kvg:09707-s2" kvg:type="㇔/㇑" d="M22.46,24.25c-0.21,4.94-2.02,10.42-3.36,15.41"/>
		<path id="kvg:09707-s3" kvg:type="㇖b/㇆" d="M23.47,26.86c21.03-2.36,43.65-5.74,62.95-6.17c8.47-0.19,1.09,6.4-0.26,7.96"/>
		<path id="kvg:09707-s4" kvg:type="㇑" d="M53.17,15.51c0.97,0.97,1.33,2.37,1.33,4.26c0,5-0.01,12.7-0.02,19.23c0,1.93,0,3.76,0,5.39"/>
		<path id="kvg:09707-s5" kvg:type="㇔" d="M33.88,32.25c3.29,0.77,7.79,3.09,9.58,4.37"/>
		<path id="kvg:09707-s6" kvg:type="㇔" d="M33.75,41.56c2.6,0.75,6.6,3.52,8.02,4.78"/>
		<path id="kvg:09707-s7" kvg:type="㇔" d="M65.5,28.78c3.66,1.16,7.81,3.47,9.28,4.43"/>
		<path id="kvg:09707-s8" kvg:type="㇔" d="M65.75,37.88c2.83,0.87,6.7,3.48,8.24,4.94"/>
	</g>
	<g id="kvg:09707-g2" kvg:element="辰" kvg:position="bottom" kvg:phon="辰">
		<g id="kvg:09707-g3" kvg:element="厂">
			<path id="kvg:09707-s9" kvg:type="㇐" d="M30.74,52.3c2.91,0.86,5.38,0.86,8.39,0.59c9.18-0.81,23.91-2.53,31.49-3.29c2.74-0.27,5.66-0.71,8.39-0.23"/>
			<path id="kvg:09707-s10" kvg:type="㇒" d="M32.4,53.17c0.65,0.65,0.68,2.03,0.4,3.68C30.75,69,28.5,83.12,15.25,95.3"/>
		</g>
		<path id="kvg:09707-s11" kvg:type="㇐" d="M41.92,61.07c1.78,0.6,3.78,0.28,5.59,0.06c5.11-0.6,12.68-1.78,17.74-2.35c1.78-0.2,3.42-0.28,5.19,0.08"/>
		<path id="kvg:09707-s12" kvg:type="㇐" d="M33.56,70.42c2.16,0.73,5.23,0.38,7.45,0.15c8.51-0.89,25.71-2.7,33.74-3.32c2.56-0.2,5.68-0.64,8.23-0.13"/>
		<path id="kvg:09707-s13" kvg:type="㇙" d="M43.42,73.66c0.9,0.9,1.33,2.34,1.33,3.96c0,9.36,0.07,15.19,0.07,16.78c0,1.58,1.26,2.5,3.08,1.05c2.73-2.19,8.48-6.44,12.6-9.19"/>
		<path id="kvg:09707-s14" kvg:type="㇒" d="M79.36,73.09c0.06,0.27,0.12,0.69-0.11,1.07c-0.78,1.3-3.68,3.49-8.04,5.7"/>
		<path id="kvg:09707-s15" kvg:type="㇏" d="M56.7,75.13c2.55,0,19.54,11.68,27.12,16.69c2.73,1.81,4.55,2.92,8.18,4.12"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09707" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 28.50 14.50)">1</text>
	<text transform="matrix(1 0 0 1 15.50 27.50)">2</text>
	<text transform="matrix(1 0 0 1 25.50 23.50)">3</text>
	<text transform="matrix(1 0 0 1 45.50 21.50)">4</text>
	<text transform="matrix(1 0 0 1 26.50 35.50)">5</text>
	<text transform="matrix(1 0 0 1 26.50 43.55)">6</text>
	<text transform="matrix(1 0 0 1 58.50 31.63)">7</text>
	<text transform="matrix(1 0 0 1 58.50 40.55)">8</text>
	<text transform="matrix(1 0 0 1 33.75 50.50)">9</text>
	<text transform="matrix(1 0 0 1 21.75 57.50)">10</text>
	<text transform="matrix(1 0 0 1 38.25 59.50)">11</text>
	<text transform="matrix(1 0 0 1 35.50 68.50)">12</text>
	<text transform="matrix(1 0 0 1 34.50 80.50)">13</text>
	<text transform="matrix(1 0 0 1 68.50 75.50)">14</text>
	<text transform="matrix(1 0 0 1 49.50 79.50)">15</text>
</g>
</svg>
