<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09aad" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09aad" kvg:element="骭">
	<g id="kvg:09aad-g1" kvg:element="骨" kvg:position="left" kvg:radical="general">
		<g id="kvg:09aad-g2" kvg:position="top">
			<path id="kvg:09aad-s1" kvg:type="㇑a" d="M15.62,14.2c0.25,0.48,0.96,2.54,0.99,3.17c0.14,2.88,2.09,18.34,2.09,18.66"/>
			<path id="kvg:09aad-s2" kvg:type="㇕b" d="M16.81,16.13C18.22,16.1,32.5,14,39.09,12.56c1.41-0.31,2.91,1.19,2.68,2.39c-0.66,3.41-2.25,11.52-2.91,19.72"/>
			<path id="kvg:09aad-s3" kvg:type="㇑a" d="M28.07,24.12c0.25,0.48,0.46,1.54,0.49,2.17c0.15,2.81,0.32,8.98,0.31,9.29"/>
			<path id="kvg:09aad-s4" kvg:type="㇐a" d="M28.98,24.91c1.41-0.03,9.45-1.47,10.73-1.64"/>
			<path id="kvg:09aad-s5" kvg:type="㇔" d="M12.04,36.62c-0.02,3.61-1.85,9-2.31,11.38"/>
			<path id="kvg:09aad-s6" kvg:type="㇖b" d="M12.19,38.26c5.8-1.18,30.14-4.09,33.94-4.29c5.94-0.32,0.23,5.3-1.25,7.24"/>
		</g>
		<g id="kvg:09aad-g3" kvg:element="月" kvg:position="bottom">
			<path id="kvg:09aad-s7" kvg:type="㇑" d="M18.81,44.12c0.37,0.7,0.62,1.4,0.74,2.1c0.12,0.7,0.18,50.51,0.12,52.08"/>
			<path id="kvg:09aad-s8" kvg:type="㇆a" d="M20.29,45.82c1.49-0.17,15.14-2.65,16.25-2.85c1.98-0.35,2.73,2.27,2.48,3.32c-0.24,1.01-0.37,29.87-0.37,44.04c0,9.09-4.98,3.15-6.22,1.57"/>
			<path id="kvg:09aad-s9" kvg:type="㇐a" d="M20.29,59.31c6.46-0.81,13.73-1.64,18.2-2.17"/>
			<path id="kvg:09aad-s10" kvg:type="㇐a" d="M19.99,72.67c3.97-0.53,13.67-1.62,18.26-1.97"/>
		</g>
	</g>
	<g id="kvg:09aad-g4" kvg:element="干" kvg:position="right">
		<path id="kvg:09aad-s11" kvg:type="㇐" d="M55.17,16.12c0.8,0.43,3.56,0.46,4.36,0.38c5.54-0.5,20.84-2.16,30.29-3c1.33-0.12,4.13,0.37,4.79,0.58"/>
		<g id="kvg:09aad-g5" kvg:element="十">
			<path id="kvg:09aad-s12" kvg:type="㇐" d="M49.38,46.28c1.29,0.46,3.67,0.59,4.96,0.46c12.16-1.24,25.65-2.99,39.76-3.41c2.16-0.06,3.45,0.22,4.53,0.45"/>
			<path id="kvg:09aad-s13" kvg:type="㇑" d="M72.45,16.63c0.47,0.45,2,1.85,2,4.23c0,4.62-0.06,72.71-0.16,78.27"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09aad" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 10.85 21.50)">1</text>
	<text transform="matrix(1 0 0 1 18.66 13.85)">2</text>
	<text transform="matrix(1 0 0 1 22.50 29.50)">3</text>
	<text transform="matrix(1 0 0 1 28.54 22.50)">4</text>
	<text transform="matrix(1 0 0 1 3.72 43.85)">5</text>
	<text transform="matrix(1 0 0 1 12.58 34.85)">6</text>
	<text transform="matrix(1 0 0 1 12.50 54.50)">7</text>
	<text transform="matrix(1 0 0 1 21.60 43.50)">8</text>
	<text transform="matrix(1 0 0 1 23.59 55.50)">9</text>
	<text transform="matrix(1 0 0 1 22.94 68.50)">10</text>
	<text transform="matrix(1 0 0 1 52.50 13.50)">11</text>
	<text transform="matrix(1 0 0 1 51.50 43.50)">12</text>
	<text transform="matrix(1 0 0 1 63.50 25.73)">13</text>
</g>
</svg>
