<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09d28" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09d28" kvg:element="鴨">
	<g id="kvg:09d28-g1" kvg:element="甲" kvg:position="left">
		<g id="kvg:09d28-g2" kvg:element="日" kvg:partial="true">
			<path id="kvg:09d28-s1" kvg:type="㇑" d="M11.5,22.49c0.88,0.88,1.38,2.04,1.47,2.84c0.86,7.84,1.78,17.64,2.7,28.05c0.18,2.09,0.37,4.22,0.55,6.37"/>
			<path id="kvg:09d28-s2" kvg:type="㇕" d="M13.69,24.37c8.22-1.4,20.54-2.6,26.96-3.27c3.55-0.37,4.79,1.29,4.48,4.78c-0.52,5.81-1.36,15.68-2.04,23.14c-0.3,3.27-0.56,6.08-0.75,7.87"/>
			<path id="kvg:09d28-s3" kvg:type="㇐" d="M15.48,40.18c7.64-1.06,22.63-2.37,27.41-2.8"/>
			<path id="kvg:09d28-s4" kvg:type="㇐" d="M17.14,56.96c8.11-1.08,15.82-2.1,24.13-2.72"/>
		</g>
		<g id="kvg:09d28-g3" kvg:element="丨" kvg:radical="nelson">
			<path id="kvg:09d28-s5" kvg:type="㇑" d="M27.36,24.44c1.16,1.16,1.44,2.28,1.44,4.06c-0.02,6.82,0.02,40.35,0.04,57.38c0.01,5.4,0.01,9.14,0.01,9.84"/>
		</g>
	</g>
	<g id="kvg:09d28-g4" kvg:element="鳥" kvg:position="right" kvg:radical="tradit">
		<path id="kvg:09d28-s6" kvg:type="㇒" d="M70.03,10.66c0.08,0.94-0.06,1.84-0.57,2.64c-1.84,2.95-3.72,5.07-7.88,8.9"/>
		<path id="kvg:09d28-s7" kvg:type="㇑" d="M57.55,24.17c0.76,0.76,0.98,1.91,0.85,2.95c0,6.43,0.02,20.75-0.06,32c-0.02,3.56-0.06,7.56-0.1,10.16"/>
		<path id="kvg:09d28-s8" kvg:type="㇕a" d="M58.81,24.75c8.31-1.25,16.69-2.62,24.01-3.31c2.23-0.21,3.33,2.15,3.13,4.02c-0.1,0.9-1.02,6.98-1.89,13.68c-0.24,1.83-0.47,3.7-0.69,5.53"/>
		<path id="kvg:09d28-s9" kvg:type="㇐a" d="M59.56,34.33c5.06-0.46,15.31-1.71,24.5-2.34"/>
		<path id="kvg:09d28-s10" kvg:type="㇐a" d="M59.72,44.87c5.53-0.49,17.8-1.73,22.63-1.75"/>
		<path id="kvg:09d28-s11" kvg:type="㇐b" d="M59.44,56.36c6.67-0.69,22.31-2.11,29.71-2.75c2.51-0.22,5-0.28,7.52-0.24"/>
		<path id="kvg:09d28-s12" kvg:type="㇆a" d="M58.24,69.5c9.1-1.63,26.88-3.42,31.92-3.9c3.28-0.31,4.97,0.89,4.38,4.57c-1.66,10.37-3.92,17.57-7.68,23.75c-3.61,5.93-5.97,1.04-7.3-0.24"/>
		<g id="kvg:09d28-g5" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09d28-s13" kvg:type="㇔" d="M50.02,80.48c0.4,3.71-0.29,8.91-1.46,11.19"/>
			<path id="kvg:09d28-s14" kvg:type="㇔" d="M59.57,77.99c1.84,1.84,3.58,6.67,4.04,9.37"/>
			<path id="kvg:09d28-s15" kvg:type="㇔" d="M69.7,76.29c1.34,1.42,3.46,5.68,3.8,7.76"/>
			<path id="kvg:09d28-s16" kvg:type="㇔" d="M78.41,73.48c1.71,1.35,4.43,5.41,4.86,7.39"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09d28" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 4.50 27.50)">1</text>
	<text transform="matrix(1 0 0 1 15.50 20.50)">2</text>
	<text transform="matrix(1 0 0 1 18.50 36.40)">3</text>
	<text transform="matrix(1 0 0 1 19.50 53.50)">4</text>
	<text transform="matrix(1 0 0 1 31.50 30.50)">5</text>
	<text transform="matrix(1 0 0 1 61.25 11.50)">6</text>
	<text transform="matrix(1 0 0 1 50.25 31.50)">7</text>
	<text transform="matrix(1 0 0 1 75.50 19.10)">8</text>
	<text transform="matrix(1 0 0 1 63.83 31.03)">9</text>
	<text transform="matrix(1 0 0 1 62.18 42.50)">10</text>
	<text transform="matrix(1 0 0 1 62.44 53.50)">11</text>
	<text transform="matrix(1 0 0 1 62.25 65.50)">12</text>
	<text transform="matrix(1 0 0 1 41.50 79.50)">13</text>
	<text transform="matrix(1 0 0 1 52.50 83.50)">14</text>
	<text transform="matrix(1 0 0 1 62.50 81.50)">15</text>
	<text transform="matrix(1 0 0 1 71.50 79.50)">16</text>
</g>
</svg>
