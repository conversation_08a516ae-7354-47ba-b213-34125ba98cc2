<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09ede" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09ede" kvg:element="點">
	<g id="kvg:09ede-g1" kvg:element="黑" kvg:variant="true" kvg:original="黒" kvg:position="left" kvg:radical="general">
		<g id="kvg:09ede-g2" kvg:element="里" kvg:variant="true" kvg:position="top">
			<path id="kvg:09ede-s1" kvg:type="㇑" d="M14.5,19.21c0.29,0.42,0.66,1.63,0.82,2.16c1.33,4.33,2.58,15.24,3.41,23.33"/>
			<path id="kvg:09ede-s2" kvg:type="㇕" d="M15.62,21.17c9.44-0.99,27.35-3.64,34.4-4.47c2.59-0.31,3.9,1.21,3.78,2.78c-0.31,4-1.9,14.23-3.81,21.26"/>
			<path id="kvg:09ede-s3" kvg:type="㇔" d="M22.15,26.44c1.9,1.37,4.9,5.65,5.37,7.78"/>
			<path id="kvg:09ede-s4" kvg:type="㇒" d="M45.75,23.37c0.02,0.22,0.04,0.56-0.04,0.87c-0.44,1.83-2.94,5.84-6.36,8.3"/>
			<path id="kvg:09ede-s5" kvg:type="㇐" d="M18.89,41.8c6.8-0.42,21.93-3.11,31.32-3.8"/>
			<path id="kvg:09ede-s6" kvg:type="㇑" d="M32.91,21.05c0.52,0.63,0.77,1.01,0.79,1.78c0.24,8.9,0.1,38.88,0.1,42.01"/>
			<path id="kvg:09ede-s7" kvg:type="㇐" d="M15.9,52.33c1,0.31,2.84,0.91,3.84,0.81C30.5,52,39,50.75,46.95,49.7c1.66-0.22,2.67,0.15,3.51,0.3"/>
			<path id="kvg:09ede-s8" kvg:type="㇀/㇐" d="M14.01,69.69c0.81,0.75,2.85,0.97,3.63,0.75c10.79-3.04,19.29-5.72,30.62-9.44"/>
		</g>
		<g id="kvg:09ede-g3" kvg:element="灬" kvg:variant="true" kvg:original="火" kvg:position="bottom">
			<path id="kvg:09ede-s9" kvg:type="㇔" d="M15.63,82.58c-0.52,5.55-3.02,12.03-3.7,13.41"/>
			<path id="kvg:09ede-s10" kvg:type="㇔" d="M23.84,78.23c2.66,3.77,4.18,10.52,3.47,14.61"/>
			<path id="kvg:09ede-s11" kvg:type="㇔" d="M34.18,74.88c2.57,3.37,5.17,9.68,5.17,13.61"/>
			<path id="kvg:09ede-s12" kvg:type="㇔" d="M45.36,71.59c2.28,2.03,5.88,8.36,6.45,11.52"/>
		</g>
	</g>
	<g id="kvg:09ede-g4" kvg:element="占" kvg:position="right">
		<g id="kvg:09ede-g5" kvg:element="卜" kvg:variant="true" kvg:original="ト" kvg:position="top">
			<path id="kvg:09ede-s13" kvg:type="㇑a" d="M70.81,14.63c0.76,0.45,2.51,1.87,2.51,4.25c0,5.87-0.25,30.12-0.25,43.25"/>
			<path id="kvg:09ede-s14" kvg:type="㇐b" d="M73.75,36.62c8.64-1.08,18.23-3.23,19.43-3.23c1.2,0,3.36,0,4.32,0.36"/>
		</g>
		<g id="kvg:09ede-g6" kvg:element="口" kvg:position="bottom">
			<path id="kvg:09ede-s15" kvg:type="㇑" d="M56.91,64.05c0.41,0.46,0.82,0.84,1,1.41c1.41,4.54,4.09,22.55,5.05,30.7"/>
			<path id="kvg:09ede-s16" kvg:type="㇕b" d="M58.71,65.69C69.5,64,86.34,61.62,91.46,60.98c1.87-0.23,2.99,2.36,2.73,3.75c-1.11,5.71-2.49,18.04-4.02,25.28"/>
			<path id="kvg:09ede-s17" kvg:type="㇐b" d="M63.36,92.66c9.14-0.91,20.89-2.16,29.68-2.94"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09ede" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 9.50 27.50)">1</text>
	<text transform="matrix(1 0 0 1 16.50 17.50)">2</text>
	<text transform="matrix(1 0 0 1 25.50 27.50)">3</text>
	<text transform="matrix(1 0 0 1 39.50 24.50)">4</text>
	<text transform="matrix(1 0 0 1 21.50 38.50)">5</text>
	<text transform="matrix(1 0 0 1 36.50 29.50)">6</text>
	<text transform="matrix(1 0 0 1 9.50 53.50)">7</text>
	<text transform="matrix(1 0 0 1 6.50 70.50)">8</text>
	<text transform="matrix(1 0 0 1 9.50 83.50)">9</text>
	<text transform="matrix(1 0 0 1 17.50 86.50)">10</text>
	<text transform="matrix(1 0 0 1 29.50 82.50)">11</text>
	<text transform="matrix(1 0 0 1 39.50 78.50)">12</text>
	<text transform="matrix(1 0 0 1 61.50 11.50)">13</text>
	<text transform="matrix(1 0 0 1 76.50 32.50)">14</text>
	<text transform="matrix(1 0 0 1 48.50 71.50)">15</text>
	<text transform="matrix(1 0 0 1 59.50 61.50)">16</text>
	<text transform="matrix(1 0 0 1 65.50 88.50)">17</text>
</g>
</svg>
