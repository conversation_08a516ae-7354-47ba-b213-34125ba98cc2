<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09a69" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09a69" kvg:element="驩">
	<g id="kvg:09a69-g1" kvg:element="馬" kvg:position="left" kvg:radical="general">
		<path id="kvg:09a69-s1" kvg:type="㇑" d="M13.74,16.38c1.01,1.12,1,3.4,0.99,4.86c-0.05,10.36-0.29,39.9-0.98,42.8"/>
		<path id="kvg:09a69-s2" kvg:type="㇐b" d="M15.27,18c7.82-0.57,21.4-2.52,23.76-2.9c0.94-0.15,2.53-0.27,3-0.03"/>
		<path id="kvg:09a69-s3" kvg:type="㇑a" d="M27.31,18.06c0.25,0.41,0.96,0.95,0.95,1.61c-0.02,6.69-0.06,30.3-0.25,40.82"/>
		<path id="kvg:09a69-s4" kvg:type="㇐b" d="M15.16,32.48c6.26-0.63,18.25-2.73,20.94-3.16c0.94-0.15,2.53-0.26,3-0.03"/>
		<path id="kvg:09a69-s5" kvg:type="㇐b" d="M14.76,45.61c6.26-0.64,18.75-2.77,21.44-3.2c0.94-0.15,2.53-0.27,3-0.03"/>
		<path id="kvg:09a69-s6" kvg:type="㇆a" d="M14.49,63.39c6.17-1.53,22.03-3.92,25.43-4.52c2.08-0.37,4.19,0.54,3.95,4.49C43.2,74.57,40,87.75,35.75,95.28c-3.35,5.93-5.32,0.44-6.21-0.93"/>
		<g id="kvg:09a69-g2" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09a69-s7" kvg:type="㇔" d="M12.49,75.65c0.42,5.86-1.36,10.52-1.81,11.69"/>
			<path id="kvg:09a69-s8" kvg:type="㇔" d="M19,73.28c1.08,2.28,2,5.15,2.25,9.21"/>
			<path id="kvg:09a69-s9" kvg:type="㇔" d="M25.94,70.68c0.56,1.11,2.56,4.79,2.81,8.43"/>
			<path id="kvg:09a69-s10" kvg:type="㇔" d="M32.75,66.96c1.25,2.18,3.3,4.43,3.75,7.32"/>
		</g>
	</g>
	<g id="kvg:09a69-g3" kvg:position="right">
		<g id="kvg:09a69-g4" kvg:element="艹" kvg:variant="true" kvg:original="艸" kvg:position="top">
			<path id="kvg:09a69-s11" kvg:type="㇐" d="M46.13,19.62c1.35,0.55,2.91,0.66,4.26,0.55c12.37-1.01,28.63-3.86,42.9-3.93c2.24-0.01,3.59,0.26,4.71,0.54"/>
			<path id="kvg:09a69-s12" kvg:type="㇑a" d="M60.11,11.48c0.93,0.96,1.23,1.25,1.33,1.85c0.93,5.54,0.73,11.25,1,13.26"/>
			<path id="kvg:09a69-s13" kvg:type="㇑a" d="M81.26,10.28c0.16,0.81,0.38,1.41,0.2,2.41c-1.08,5.85-1.07,7.28-2.24,12.78"/>
		</g>
		<g id="kvg:09a69-g5" kvg:position="bottom">
			<g id="kvg:09a69-g6" kvg:element="口">
				<path id="kvg:09a69-s14" kvg:type="㇑" d="M48.67,31.43c0.27,0.24,0.54,0.44,0.66,0.74c0.93,2.37,2.04,8.92,2.68,13.18"/>
				<path id="kvg:09a69-s15" kvg:type="㇕b" d="M50.34,33.03c5.49-1.24,12.17-2.1,15.31-2.41c1.15-0.11,1.84,0.68,1.68,1.34c-0.68,2.77-1.48,5.61-2.49,9.45"/>
				<path id="kvg:09a69-s16" kvg:type="㇐b" d="M52.14,43.26c3.69-0.35,9.86-1.47,14.93-2.01"/>
			</g>
			<g id="kvg:09a69-g7" kvg:element="口">
				<path id="kvg:09a69-s17" kvg:type="㇑" d="M72.75,28.22c0.26,0.26,0.53,0.48,0.65,0.8c0.91,2.57,2.01,8.6,2.63,13.22"/>
				<path id="kvg:09a69-s18" kvg:type="㇕b" d="M74.39,29.96c5.39-1.34,13.96-2.29,17.04-2.63c1.13-0.12,1.81,0.73,1.65,1.46c-0.67,3-1.45,5.04-2.44,9.19"/>
				<path id="kvg:09a69-s19" kvg:type="㇐b" d="M76.16,39.98c3.63-0.38,11.13-1.04,16.11-1.63"/>
			</g>
			<g id="kvg:09a69-g8" kvg:element="隹">
				<g id="kvg:09a69-g9" kvg:element="亻" kvg:variant="true" kvg:original="人">
					<path id="kvg:09a69-s20" kvg:type="㇒" d="M60.77,47.3c0.13,1.12-0.04,2.6-0.44,3.56C57.73,57,54.44,62.2,48.5,69.67"/>
					<path id="kvg:09a69-s21" kvg:type="㇑" d="M55.6,59.64c0.59,0.64,1.05,2.04,1.09,3.03C57.04,71.5,56.2,95.41,56.5,100"/>
				</g>
				<path id="kvg:09a69-s22" kvg:type="㇒" d="M78.28,46.34c0.04,0.51-0.01,1.17-0.16,1.61c-0.94,2.77-2.14,4.91-4.3,8.28"/>
				<path id="kvg:09a69-s23" kvg:type="㇐b" d="M56.52,59.83C63.4,59,92.1,56.58,95.06,56.13"/>
				<path id="kvg:09a69-s24" kvg:type="㇑a" d="M73.79,58.68c0.33,0.32,0.6,0.77,0.6,1.33c0,5.73,0.04,26.21-0.16,35.23"/>
				<path id="kvg:09a69-s25" kvg:type="㇐b" d="M57.44,70.76c6.5-0.69,31.51-3.26,34.3-3.63"/>
				<path id="kvg:09a69-s26" kvg:type="㇐b" d="M57.22,83.38c6.86-0.58,32.5-2.83,35.44-3.14"/>
				<path id="kvg:09a69-s27" kvg:type="㇐b" d="M56.65,97.16c6.89-0.83,38.38-2.55,41.34-3.01"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09a69" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 8.85 26.50)">1</text>
	<text transform="matrix(1 0 0 1 16.66 14.50)">2</text>
	<text transform="matrix(1 0 0 1 31.50 24.85)">3</text>
	<text transform="matrix(1 0 0 1 18.50 29.50)">4</text>
	<text transform="matrix(1 0 0 1 18.68 42.50)">5</text>
	<text transform="matrix(1 0 0 1 18.54 59.50)">6</text>
	<text transform="matrix(1 0 0 1 6.50 78.50)">7</text>
	<text transform="matrix(1 0 0 1 14.50 79.50)">8</text>
	<text transform="matrix(1 0 0 1 22.59 77.50)">9</text>
	<text transform="matrix(1 0 0 1 27.50 72.50)">10</text>
	<text transform="matrix(1 0 0 1 43.50 17.50)">11</text>
	<text transform="matrix(1 0 0 1 51.50 8.50)">12</text>
	<text transform="matrix(1 0 0 1 73.50 7.50)">13</text>
	<text transform="matrix(1 0 0 1 40.50 38.65)">14</text>
	<text transform="matrix(1 0 0 1 51.25 29.50)">15</text>
	<text transform="matrix(1 0 0 1 54.50 40.50)">16</text>
	<text transform="matrix(1 0 0 1 68.25 39.50)">17</text>
	<text transform="matrix(1 0 0 1 82.50 25.50)">18</text>
	<text transform="matrix(1 0 0 1 78.50 37.50)">19</text>
	<text transform="matrix(1 0 0 1 50.50 52.50)">20</text>
	<text transform="matrix(1 0 0 1 48.50 76.50)">21</text>
	<text transform="matrix(1 0 0 1 68.50 48.50)">22</text>
	<text transform="matrix(1 0 0 1 62.50 56.50)">23</text>
	<text transform="matrix(1 0 0 1 77.50 65.50)">24</text>
	<text transform="matrix(1 0 0 1 60.62 67.50)">25</text>
	<text transform="matrix(1 0 0 1 60.50 79.50)">26</text>
	<text transform="matrix(1 0 0 1 60.54 93.50)">27</text>
</g>
</svg>
