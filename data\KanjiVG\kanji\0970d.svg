<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0970d" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0970d" kvg:element="霍">
	<g id="kvg:0970d-g1" kvg:element="雨" kvg:variant="true" kvg:position="top" kvg:radical="general">
		<path id="kvg:0970d-s1" kvg:type="㇐" d="M32.16,14.53c1.54,0.06,4.39,0.32,5.88,0.17c8.01-0.81,28.18-2.33,35.39-2.45c2.1-0.03,2.7-0.16,4.63,0.11"/>
		<path id="kvg:0970d-s2" kvg:type="㇔/㇑" d="M20.43,23.67c-0.31,5.64-3.02,11.89-5.03,17.59"/>
		<path id="kvg:0970d-s3" kvg:type="㇖b/㇆" d="M19.85,26.32c7.4-1.02,52.34-3.84,68.05-3.84c9.1,0,1.17,6.33-0.28,7.93"/>
		<path id="kvg:0970d-s4" kvg:type="㇑" d="M52.92,16.82c0.91,1.36,1.31,2.26,1.33,3.6c0.01,0.48-0.27,21.77-0.27,23.09"/>
		<path id="kvg:0970d-s5" kvg:type="㇔" d="M35.5,29.93c2.99,0.68,7.08,2.71,8.71,3.83"/>
		<path id="kvg:0970d-s6" kvg:type="㇔" d="M31,38.01c3.08,0.72,7.84,3.37,9.52,4.57"/>
		<path id="kvg:0970d-s7" kvg:type="㇔" d="M66.75,27.77c4.35,1.34,9.29,4.02,11.03,5.13"/>
		<path id="kvg:0970d-s8" kvg:type="㇔" d="M66.54,37.73c4.11,1.14,9.71,4.56,11.95,6.46"/>
	</g>
	<g id="kvg:0970d-g2" kvg:element="隹" kvg:position="bottom">
		<g id="kvg:0970d-g3" kvg:element="亻" kvg:variant="true" kvg:original="人">
			<path id="kvg:0970d-s9" kvg:type="㇒" d="M35.36,46.5c0.2,1.15-0.06,2.66-0.7,3.65c-4.09,6.3-9.28,11.62-18.66,19.29"/>
			<path id="kvg:0970d-s10" kvg:type="㇑" d="M28.02,61.41c0.92,0.62,1.12,1.98,1.18,2.94c0.55,8.57-0.75,29.69-0.29,34.15"/>
		</g>
		<path id="kvg:0970d-s11" kvg:type="㇒" d="M59.14,45c0.07,0.57-0.02,1.31-0.25,1.8c-1.5,3.1-3.4,5.5-6.83,9.28"/>
		<path id="kvg:0970d-s12" kvg:type="㇐b" d="M29.44,58.59c10.65-0.81,53.16-4.16,57.73-4.6"/>
		<path id="kvg:0970d-s13" kvg:type="㇑a" d="M53.65,57.48c0.51,0.32,0.92,0.75,0.92,1.3c0,5.56,0.06,25.45-0.25,34.2"/>
		<path id="kvg:0970d-s14" kvg:type="㇐b" d="M29.86,68.63c10.05-0.67,47.39-3.61,51.7-3.98"/>
		<path id="kvg:0970d-s15" kvg:type="㇐b" d="M29.64,80.42c10.6-0.56,46.79-3.21,51.34-3.51"/>
		<path id="kvg:0970d-s16" kvg:type="㇐b" d="M29.64,94.33c10.65-0.81,54.04-2.35,58.61-2.79"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0970d" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 26.50 14.50)">1</text>
	<text transform="matrix(1 0 0 1 13.50 26.50)">2</text>
	<text transform="matrix(1 0 0 1 22.50 22.50)">3</text>
	<text transform="matrix(1 0 0 1 46.50 21.50)">4</text>
	<text transform="matrix(1 0 0 1 26.50 32.50)">5</text>
	<text transform="matrix(1 0 0 1 23.50 39.85)">6</text>
	<text transform="matrix(1 0 0 1 59.63 31.50)">7</text>
	<text transform="matrix(1 0 0 1 59.60 40.50)">8</text>
	<text transform="matrix(1 0 0 1 27.50 47.50)">9</text>
	<text transform="matrix(1 0 0 1 19.25 74.95)">10</text>
	<text transform="matrix(1 0 0 1 48.50 50.50)">11</text>
	<text transform="matrix(1 0 0 1 38.50 55.50)">12</text>
	<text transform="matrix(1 0 0 1 58.50 63.50)">13</text>
	<text transform="matrix(1 0 0 1 32.50 65.65)">14</text>
	<text transform="matrix(1 0 0 1 32.50 77.50)">15</text>
	<text transform="matrix(1 0 0 1 32.54 90.50)">16</text>
</g>
</svg>
