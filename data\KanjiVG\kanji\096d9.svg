<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_096d9" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:096d9" kvg:element="雙">
	<g id="kvg:096d9-g1" kvg:position="top">
		<g id="kvg:096d9-g2" kvg:element="隹" kvg:position="left" kvg:radical="tradit">
			<g id="kvg:096d9-g3" kvg:element="亻" kvg:variant="true" kvg:original="人">
				<path id="kvg:096d9-s1" kvg:type="㇒" d="M27.37,11c0.13,1.28-0.04,2.96-0.47,4.06c-2.77,7-6.29,12.92-12.65,21.44"/>
				<path id="kvg:096d9-s2" kvg:type="㇑" d="M22.75,26.92c0.46,0.53,0.82,1.69,0.85,2.5c0.28,7.29-0.38,29.04-0.15,32.83"/>
			</g>
			<path id="kvg:096d9-s3" kvg:type="㇒" d="M42.6,12.3c0.05,0.63-0.01,1.45-0.19,1.99c-1.11,3.43-2.53,6.08-5.08,10.25"/>
			<path id="kvg:096d9-s4" kvg:type="㇐b" d="M23.48,27.38c6.52-0.7,24.78-2.38,27.1-2.76"/>
			<path id="kvg:096d9-s5" kvg:type="㇑a" d="M37.12,28.33c0.26,0.27,0.47,0.64,0.47,1.1c0,4.73,0.03,20.05-0.13,27.49"/>
			<path id="kvg:096d9-s6" kvg:type="㇐b" d="M24.19,37.24c5.09-0.57,21.77-2.22,23.95-2.53"/>
			<path id="kvg:096d9-s7" kvg:type="㇐b" d="M23.58,46.54c5.36-0.48,22.98-1.75,25.28-2.01"/>
			<path id="kvg:096d9-s8" kvg:type="㇐b" d="M23.58,58.5c5.39-0.69,26.61-2,28.92-2.37"/>
		</g>
		<g id="kvg:096d9-g4" kvg:element="隹" kvg:position="right" kvg:radical="tradit">
			<g id="kvg:096d9-g5" kvg:element="亻" kvg:variant="true" kvg:original="人">
				<path id="kvg:096d9-s9" kvg:type="㇒" d="M64.88,11c0.11,1.19-0.03,2.74-0.39,3.75C62.19,21.22,59.27,26.7,54,34.58"/>
				<path id="kvg:096d9-s10" kvg:type="㇑" d="M60.68,25.63c0.52,0.52,0.91,1.65,0.95,2.45c0.31,7.14-0.42,27.46-0.16,31.17"/>
			</g>
			<path id="kvg:096d9-s11" kvg:type="㇒" d="M80.96,10.77c0.06,0.58-0.01,1.33-0.2,1.83c-1.18,3.16-2.68,5.59-5.38,9.44"/>
			<path id="kvg:096d9-s12" kvg:type="㇐b" d="M61.48,25.08c7.25-0.68,27.56-2.33,30.13-2.7"/>
			<path id="kvg:096d9-s13" kvg:type="㇑a" d="M76.1,25.03c0.29,0.26,0.52,0.62,0.52,1.08c0,4.63,0.03,20.61-0.14,27.9"/>
			<path id="kvg:096d9-s14" kvg:type="㇐b" d="M62.28,34.74c5.65-0.56,24.2-2.18,26.62-2.48"/>
			<path id="kvg:096d9-s15" kvg:type="㇐b" d="M61.59,43.85c5.96-0.47,25.55-1.72,28.11-1.97"/>
			<path id="kvg:096d9-s16" kvg:type="㇐b" d="M61.59,55.56c5.99-0.67,29.59-1.96,32.16-2.32"/>
		</g>
	</g>
	<g id="kvg:096d9-g6" kvg:element="又" kvg:position="bottom" kvg:radical="nelson">
		<path id="kvg:096d9-s17" kvg:type="㇇" d="M34,67.96c1.89,0.27,2.13,0.67,5.92,0.27c3.79-0.4,25.51-3.37,28.11-3.77s4.11,2.05,3.05,3.44C58.75,84,42.25,91.75,19.51,99.5"/>
		<path id="kvg:096d9-s18" kvg:type="㇏" d="M33.48,74.9C42,74.5,66.79,89.99,85.32,96.15c2.88,0.96,4.99,1.6,7.67,1.92"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_096d9" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 19.50 10.50)">1</text>
	<text transform="matrix(1 0 0 1 16.25 44.05)">2</text>
	<text transform="matrix(1 0 0 1 35.25 11.50)">3</text>
	<text transform="matrix(1 0 0 1 28.50 24.50)">4</text>
	<text transform="matrix(1 0 0 1 40.50 32.50)">5</text>
	<text transform="matrix(1 0 0 1 26.50 34.50)">6</text>
	<text transform="matrix(1 0 0 1 26.50 44.50)">7</text>
	<text transform="matrix(1 0 0 1 26.50 55.50)">8</text>
	<text transform="matrix(1 0 0 1 56.50 12.50)">9</text>
	<text transform="matrix(1 0 0 1 51.50 43.50)">10</text>
	<text transform="matrix(1 0 0 1 71.25 10.50)">11</text>
	<text transform="matrix(1 0 0 1 66.50 21.50)">12</text>
	<text transform="matrix(1 0 0 1 78.50 29.50)">13</text>
	<text transform="matrix(1 0 0 1 63.25 32.15)">14</text>
	<text transform="matrix(1 0 0 1 63.50 41.50)">15</text>
	<text transform="matrix(1 0 0 1 63.50 51.50)">16</text>
	<text transform="matrix(1 0 0 1 26.50 66.50)">17</text>
	<text transform="matrix(1 0 0 1 24.50 79.50)">18</text>
</g>
</svg>
