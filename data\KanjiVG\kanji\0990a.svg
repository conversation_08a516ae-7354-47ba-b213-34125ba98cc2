<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0990a" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0990a" kvg:element="養">
	<g id="kvg:0990a-g1" kvg:element="羊" kvg:variant="true" kvg:position="top" kvg:radical="nelson" kvg:phon="羊">
		<g id="kvg:0990a-g2" kvg:element="䒑">
			<path id="kvg:0990a-s1" kvg:type="㇔" d="M38.12,10.38c2.36,1.28,6.1,5.27,6.69,7.27"/>
			<path id="kvg:0990a-s2" kvg:type="㇒" d="M64.45,9.89c0.02,0.24,0.1,0.9-0.03,1.23c-1.04,2.5-3.37,5.6-6.42,8.38"/>
			<path id="kvg:0990a-s3" kvg:type="㇐" d="M31.52,23.6c1.63,0.42,4.63,0.54,6.26,0.42c10.47-0.77,22.84-2.4,33.21-3.65c2.69-0.32,4.36-0.3,5.72-0.09"/>
		</g>
		<path id="kvg:0990a-s4" kvg:type="㇑a" d="M52.68,25.21c0.81,0.81,1.44,2.04,1.44,3.29c0,2.63,0.06,8.13,0.06,13.5"/>
		<path id="kvg:0990a-s5" kvg:type="㇐" d="M31.58,33.96c1.49,0.41,3.93,0.59,5.32,0.49c10.17-0.71,18.82-1.79,34.15-3.27c2.3-0.22,4.13-0.14,5.29,0.1"/>
		<path id="kvg:0990a-s6" kvg:type="㇐" d="M17.03,45.76c3.06,0.49,6.36,0.62,8.58,0.39c16.78-1.77,36.53-4.39,57.14-5.57c3.72-0.21,5.97,0.05,7.84,0.37"/>
	</g>
	<g id="kvg:0990a-g3" kvg:element="食" kvg:position="bottom" kvg:radical="tradit">
		<path id="kvg:0990a-s7" kvg:type="㇒" d="M40.37,46.25c0.08,0.57-0.11,1.57-0.66,2.31C34.62,55.38,25.5,63.38,11.5,70.62"/>
		<path id="kvg:0990a-s8" kvg:type="㇏" d="M64.58,42.85c4.02,2.97,16.31,14.4,22.31,19.52c1.91,1.63,3.8,2.81,5.99,4.01"/>
		<path id="kvg:0990a-s9" kvg:type="㇑a" d="M51.64,48.26c0.92,0.92,1.65,2.12,1.65,3.07c0,1.8-0.01,2.65-0.01,4.95"/>
		<path id="kvg:0990a-s10" kvg:type="㇕" d="M38.83,58.34c0.79,0.66,2.83,0.8,4.29,0.62c6.3-0.78,20.28-2.73,21.78-2.8c1.59-0.08,2.6,0.59,2.5,2.29c-0.18,3.08-0.92,8.45-1.22,13.43c-0.05,0.79-0.23,3.56-0.23,4.01"/>
		<path id="kvg:0990a-s11" kvg:type="㇐a" d="M41.7,67.29c3.92-0.42,18.8-2.42,23.76-2.87"/>
		<path id="kvg:0990a-s12" kvg:type="㇐a" d="M41.82,76.11c8.8-0.86,17.3-1.98,23.25-2.47"/>
		<path id="kvg:0990a-s13" kvg:type="㇙" d="M39.26,58.71c0.83,0.83,1.4,1.79,1.4,3.1c0,0.76-0.01,30.21-0.01,32.03c0,2.28,1.23,2.9,2.72,2.19c2.83-1.36,9.73-4.79,12.12-6.42"/>
		<path id="kvg:0990a-s14" kvg:type="㇒" d="M75,76.5c0.19,0.81-0.25,1.74-0.82,2.24c-1.62,1.42-3.63,3.1-5.95,5.04"/>
		<path id="kvg:0990a-s15" kvg:type="㇏" d="M54.11,80.5c3.52,0.25,20.52,13.25,25.52,16c2.75,1.51,4.82,2.41,7.08,2.79"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0990a" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 32.25 9.13)">1</text>
	<text transform="matrix(1 0 0 1 56.50 8.50)">2</text>
	<text transform="matrix(1 0 0 1 27.75 21.13)">3</text>
	<text transform="matrix(1 0 0 1 45.75 31.63)">4</text>
	<text transform="matrix(1 0 0 1 24.75 36.20)">5</text>
	<text transform="matrix(1 0 0 1 9.75 49.63)">6</text>
	<text transform="matrix(1 0 0 1 24.75 57.13)">7</text>
	<text transform="matrix(1 0 0 1 79.99 50.60)">8</text>
	<text transform="matrix(1 0 0 1 55 50)">9</text>
	<text transform="matrix(1 0 0 1 41.44 54.95)">10</text>
	<text transform="matrix(1 0 0 1 44.44 65.38)">11</text>
	<text transform="matrix(1 0 0 1 44.44 75.80)">12</text>
	<text transform="matrix(1 0 0 1 29.44 69.73)">13</text>
	<text transform="matrix(1 0 0 1 77.44 75.66)">14</text>
	<text transform="matrix(1 0 0 1 47.44 86.08)">15</text>
</g>
</svg>
