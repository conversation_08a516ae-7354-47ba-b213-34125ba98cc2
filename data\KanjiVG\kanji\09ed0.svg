<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09ed0" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09ed0" kvg:element="黐">
	<g id="kvg:09ed0-g1" kvg:element="黍" kvg:position="left" kvg:radical="general">
		<g id="kvg:09ed0-g2" kvg:position="top">
			<g id="kvg:09ed0-g3" kvg:element="禾">
				<g id="kvg:09ed0-g4" kvg:element="丿" kvg:position="top">
					<path id="kvg:09ed0-s1" kvg:type="㇒" d="M 41.03,13.5 c 0.07,0.23 0.14,0.58 -0.13,0.9 -1.58,1.91 -10.64,6.09 -23.03,8.65"/>
				</g>
				<g id="kvg:09ed0-g5" kvg:element="木" kvg:position="bottom">
					<path id="kvg:09ed0-s2" kvg:type="㇐" d="M 15.1,30.9 c 0.42,0.18 2.1,0.54 3.22,0.55 5.43,0.05 17.72,-2.16 26.26,-2.66 1.12,-0.07 2.52,0.22 3.22,0.4"/>
					<path id="kvg:09ed0-s3" kvg:type="㇑" d="M 32.66,21.62 c 0.52,0.51 0.84,2.28 0.94,3.29 0.11,1.01 0,15.45 -0.11,21.78"/>
					<path id="kvg:09ed0-s4" kvg:type="㇒" d="M 31.95,31.43 C 28.28,36.45 21.9,43.8 13.85,46.76"/>
					<path id="kvg:09ed0-s5" kvg:type="㇔" d="M 35.12,34.8 c 4.08,1.27 11.48,8.42 13.39,11.16"/>
				</g>
			</g>
			<path id="kvg:09ed0-s6" kvg:type="㇒" d="M 31.07,49.2 c 0.05,0.43 -0.03,1.21 -0.34,1.72 C 28.23,55 22.75,61.5 12.25,67.86"/>
			<path id="kvg:09ed0-s7" kvg:type="㇏" d="M 33.53,51.58 C 37,52.8 44.85,57.74 46.84,60.5"/>
		</g>
		<g id="kvg:09ed0-g6" kvg:element="氺" kvg:variant="true" kvg:original="水" kvg:position="bottom">
			<path id="kvg:09ed0-s8" kvg:type="㇚" d="M 32.49,60.64 c 0.06,0.4 0.9,2.09 0.9,4.64 0,15.27 -0.19,22.06 -0.19,26.23 0,8.86 -5.18,1.26 -6.25,0.21"/>
			<path id="kvg:09ed0-s9" kvg:type="㇔" d="M 18.53,70.5 c 2.42,1.05 6.26,4.32 6.86,5.96"/>
			<path id="kvg:09ed0-s10" kvg:type="㇀" d="M 13.17,87.83 c 1.35,1.07 2.48,1.53 3.83,0.61 0.8,-0.54 7.22,-5.34 10.6,-7.93"/>
			<path id="kvg:09ed0-s11" kvg:type="㇒" d="M 45.65,65.21 c 0.03,0.23 0.05,0.59 -0.05,0.92 -0.6,1.95 -4.04,6.22 -8.74,8.83"/>
			<path id="kvg:09ed0-s12" kvg:type="㇔" d="M 36.51,79.09 c 2.54,1.2 9.49,6.98 11.71,11.47"/>
		</g>
	</g>
	<g id="kvg:09ed0-g7" kvg:element="离" kvg:position="right">
		<g id="kvg:09ed0-g8" kvg:element="亠">
			<path id="kvg:09ed0-s13" kvg:type="㇑a" d="M 72,12.47 c 0.64,0.29 1.7,1.4 1.7,1.97 0,2.11 -0.2,7.1 -0.08,8.88"/>
			<path id="kvg:09ed0-s14" kvg:type="㇐" d="M 52.92,24.85 c 1.55,0.25 2.91,0.53 4.69,0.35 10.6,-1.06 22.34,-2.72 34.7,-3.23 1.82,-0.07 2.74,0.03 4.1,0.45"/>
		</g>
		<g id="kvg:09ed0-g9" kvg:element="凶">
			<g id="kvg:09ed0-g10" kvg:element="乂">
				<g id="kvg:09ed0-g11" kvg:element="丿">
					<path id="kvg:09ed0-s15" kvg:type="㇒" d="M 80.88,27.65 c 0.28,0.35 0.56,0.62 0.14,1.24 -3.75,5.5 -9.34,10.1 -17.42,12.98"/>
				</g>
				<path id="kvg:09ed0-s16" kvg:type="㇔/㇏" d="M 65.62,30.3 c 5.37,1.67 13.1,7.08 15.9,11.04"/>
			</g>
			<g id="kvg:09ed0-g12" kvg:element="凵" kvg:position="kamae">
				<path id="kvg:09ed0-s17" kvg:type="㇄a" d="M 56.47,31.81 c 0.36,0.17 1.12,1.31 1.19,1.65 0.53,2.55 -0.47,13.17 -0.5,13.99 -0.04,0.82 0.79,1.28 1.5,1.18 10.59,-1.48 27.09,-2.87 30.83,-3.04"/>
				<path id="kvg:09ed0-s18" kvg:type="㇑" d="M 89.25,27.95 c 0.36,0.17 1.12,1.3 1.19,1.65 0.07,0.35 -0.71,12.15 -1.03,19"/>
			</g>
		</g>
		<g id="kvg:09ed0-g13" kvg:element="禸">
			<g id="kvg:09ed0-g14" kvg:element="冂">
				<path id="kvg:09ed0-s19" kvg:type="㇑" d="M 53.2,60.02 c 0.49,1.02 1.07,2.63 1.15,4.49 0.22,4.84 0.64,29.97 0.64,33.03"/>
				<path id="kvg:09ed0-s20" kvg:type="㇆" d="M 54.47,62.06 c 5.79,-0.67 36.19,-4.35 37.36,-4.35 1.67,0 2.74,0.5 2.74,2.01 0,3.18 -0.27,25.03 -0.27,31.59 0,9.69 -4.72,3.32 -6.28,1.72"/>
			</g>
			<g id="kvg:09ed0-g15" kvg:element="厶">
				<path id="kvg:09ed0-s21" kvg:type="㇒" d="M 73.97,49.78 c 0.25,0.51 0.32,2.49 0.08,3.11 -1.586769,9.57726 -1.639018,14.986605 -3.538691,23.107096"/>
				<path id="kvg:09ed0-s22" kvg:type="㇐" d="M 64.554029,78.531595 c 3.87,-1.35 10.89,-3.36 16.27,-5.63"/>
				<path id="kvg:09ed0-s23" kvg:type="㇔" d="M 78.43,66.55 c 2.44,2.43 6.31,9.98 6.92,13.76"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09ed0" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 32.50 12.63)">1</text>
	<text transform="matrix(1 0 0 1 8.50 31.55)">2</text>
	<text transform="matrix(1 0 0 1 26.75 28.50)">3</text>
	<text transform="matrix(1 0 0 1 18.50 39.50)">4</text>
	<text transform="matrix(1 0 0 1 43.50 36.50)">5</text>
	<text transform="matrix(1 0 0 1 23.50 50.50)">6</text>
	<text transform="matrix(1 0 0 1 38.50 51.50)">7</text>
	<text transform="matrix(1 0 0 1 26.50 66.50)">8</text>
	<text transform="matrix(1 0 0 1 12.25 75.03)">9</text>
	<text transform="matrix(1 0 0 1 3.50 88.50)">10</text>
	<text transform="matrix(1 0 0 1 37.25 65.50)">11</text>
	<text transform="matrix(1 0 0 1 40.50 80.50)">12</text>
	<text transform="matrix(1 0 0 1 61.50 11.50)">13</text>
	<text transform="matrix(1 0 0 1 51.50 21.50)">14</text>
	<text transform="matrix(1 0 0 1 71.25 30.58)">15</text>
	<text transform="matrix(1 0 0 1 61.50 36.50)">16</text>
	<text transform="matrix(1 0 0 1 48.50 37.50)">17</text>
	<text transform="matrix(1 0 0 1 93.25 31.50)">18</text>
	<text transform="matrix(1 0 0 1 46.50 72.50)">19</text>
	<text transform="matrix(1 0 0 1 55.50 58.50)">20</text>
	<text transform="matrix(1 0 0 1 64.25 55.50)">21</text>
	<text transform="matrix(1 0 0 1 59.89 86.05)">22</text>
	<text transform="matrix(1 0 0 1 79.75 66.13)">23</text>
</g>
</svg>
