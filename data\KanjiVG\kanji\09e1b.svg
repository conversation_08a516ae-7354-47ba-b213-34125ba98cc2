<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09e1b" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09e1b" kvg:element="鸛">
	<g id="kvg:09e1b-g1" kvg:position="left">
		<g id="kvg:09e1b-g2" kvg:element="艹" kvg:variant="true" kvg:original="艸" kvg:position="top">
			<path id="kvg:09e1b-s1" kvg:type="㇐" d="M12.88,23.31c1.22,0.35,2.64,0.42,3.86,0.35c11.21-0.63,22.51-1.41,31.75-2.47c2.02-0.23,2.75,0.17,3.77,0.34"/>
			<path id="kvg:09e1b-s2" kvg:type="㇑a" d="M23.07,14.91c0.95,0.95,1.25,1.23,1.35,1.81c0.95,5.44,1.76,11.04,2.03,13.02"/>
			<path id="kvg:09e1b-s3" kvg:type="㇑a" d="M41.69,12.78c0.15,0.82,0.36,1.44,0.18,2.45c-1.01,5.95-1.47,8.43-2.57,14.03"/>
		</g>
		<g id="kvg:09e1b-g3" kvg:position="bottom">
			<g id="kvg:09e1b-g4" kvg:element="口">
				<path id="kvg:09e1b-s4" kvg:type="㇑" d="M14.1,34.17c0.22,0.22,0.45,0.4,0.55,0.68c0.78,2.19,1.71,8.21,2.24,12.14"/>
				<path id="kvg:09e1b-s5" kvg:type="㇕b" d="M15.5,35.65c4.59-1.14,10.68-1.93,13.3-2.22c0.96-0.1,1.54,0.62,1.4,1.24c-0.57,2.55-1.23,5.17-2.08,8.7"/>
				<path id="kvg:09e1b-s6" kvg:type="㇐b" d="M17,45.07c3.09-0.33,8.74-1.35,12.98-1.85"/>
			</g>
			<g id="kvg:09e1b-g5" kvg:element="口">
				<path id="kvg:09e1b-s7" kvg:type="㇑" d="M36.4,32.98c0.2,0.22,0.41,0.41,0.5,0.69c0.71,2.21,1.55,7.39,2.04,11.36"/>
				<path id="kvg:09e1b-s8" kvg:type="㇕b" d="M37.67,34.48c4.17-1.15,9.25-1.97,11.64-2.26c0.87-0.11,1.4,0.63,1.27,1.25c-0.52,2.58-1.12,4.29-1.89,7.86"/>
				<path id="kvg:09e1b-s9" kvg:type="㇐b" d="M39.03,43.09c2.81-0.33,6.67-0.93,10.53-1.44"/>
			</g>
			<g id="kvg:09e1b-g6" kvg:element="隹">
				<g id="kvg:09e1b-g7" kvg:element="亻" kvg:variant="true" kvg:original="人">
					<path id="kvg:09e1b-s10" kvg:type="㇒" d="M24.94,47.21c0.14,1-0.04,2.32-0.48,3.18c-2.84,5.48-6.44,10.11-12.95,16.78"/>
					<path id="kvg:09e1b-s11" kvg:type="㇑" d="M19.56,58.29c0.47,0.56,0.83,1.77,0.86,2.63c0.28,7.66-0.38,29.34-0.15,33.33"/>
				</g>
				<path id="kvg:09e1b-s12" kvg:type="㇒" d="M38.75,47.49c0.05,0.42-0.01,0.98-0.16,1.34c-0.96,2.3-2.18,4.08-4.38,6.89"/>
				<path id="kvg:09e1b-s13" kvg:type="㇐b" d="M20.28,58.46c5.46-0.72,26.24-2.82,28.58-3.22"/>
				<path id="kvg:09e1b-s14" kvg:type="㇑a" d="M33.98,57.46c0.26,0.28,0.47,0.67,0.47,1.16c0,4.97,0.03,22.77-0.13,30.59"/>
				<path id="kvg:09e1b-s15" kvg:type="㇐b" d="M21.02,68.38c5.15-0.6,23.4-2.83,25.61-3.16"/>
				<path id="kvg:09e1b-s16" kvg:type="㇐b" d="M20.84,78.47c5.44-0.5,24.19-2.02,26.52-2.3"/>
				<path id="kvg:09e1b-s17" kvg:type="㇐b" d="M20.39,90.88c5.46-0.72,27.27-3.09,29.61-3.48"/>
			</g>
		</g>
	</g>
	<g id="kvg:09e1b-g8" kvg:element="鳥" kvg:position="right" kvg:radical="general">
		<path id="kvg:09e1b-s18" kvg:type="㇒" d="M74.54,11.16c0.02,0.33,0.05,0.86-0.04,1.33c-0.53,2.77-3.54,8.72-7.66,12.17"/>
		<path id="kvg:09e1b-s19" kvg:type="㇑" d="M59.3,25.85c0.33,0.39,0.6,0.92,0.6,1.57c0,6.57,0.04,31.8-0.17,42.13"/>
		<path id="kvg:09e1b-s20" kvg:type="㇕a" d="M60.31,27.55c1.55,0,23.5-2.88,25.01-2.78c2.24,0.14,3.33,1.95,3.13,3.65c-0.12,1.04-2.14,11.48-3.58,19.45"/>
		<path id="kvg:09e1b-s21" kvg:type="㇐a" d="M60.06,36.75c1.9,0.12,24.1-2.49,26.25-2.35"/>
		<path id="kvg:09e1b-s22" kvg:type="㇐a" d="M60.47,47.23c3.88-0.13,20.05-1.76,24.88-1.77"/>
		<path id="kvg:09e1b-s23" kvg:type="㇐b" d="M60.19,58c8.91-0.83,28.63-2.82,32.45-3.35c1.33-0.18,3.6-0.35,4.27-0.1"/>
		<path id="kvg:09e1b-s24" kvg:type="㇆a" d="M59.74,69.75c9.1-1.48,27.13-3.6,32.17-4.04c3.28-0.29,4.97,0.81,4.38,4.15c-1.64,9.3-3.58,19.13-6.43,24.85c-2.86,5.75-5.97,0.95-7.3-0.22"/>
		<g id="kvg:09e1b-g9" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09e1b-s25" kvg:type="㇔" d="M54.77,79.09c0.47,4.62-0.34,10.15-1.73,12.98"/>
			<path id="kvg:09e1b-s26" kvg:type="㇔" d="M61.57,76c1.5,2.36,2.92,8.55,3.29,12.01"/>
			<path id="kvg:09e1b-s27" kvg:type="㇔" d="M71.46,74.46c1.42,1.74,3.67,6.99,4.03,9.55"/>
			<path id="kvg:09e1b-s28" kvg:type="㇔" d="M80.41,72.86c1.8,1.68,4.66,6.74,5.11,9.21"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09e1b" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 6.50 24.63)">1</text>
	<text transform="matrix(1 0 0 1 15.50 14.50)">2</text>
	<text transform="matrix(1 0 0 1 32.50 12.50)">3</text>
	<text transform="matrix(1 0 0 1 8.50 39.50)">4</text>
	<text transform="matrix(1 0 0 1 15.50 32.50)">5</text>
	<text transform="matrix(1 0 0 1 19.50 42.50)">6</text>
	<text transform="matrix(1 0 0 1 32.50 41.50)">7</text>
	<text transform="matrix(1 0 0 1 42.50 30.50)">8</text>
	<text transform="matrix(1 0 0 1 41.50 40.50)">9</text>
	<text transform="matrix(1 0 0 1 13.50 54.95)">10</text>
	<text transform="matrix(1 0 0 1 11.50 74.88)">11</text>
	<text transform="matrix(1 0 0 1 29.50 49.50)">12</text>
	<text transform="matrix(1 0 0 1 26.25 55.50)">13</text>
	<text transform="matrix(1 0 0 1 37.50 63.50)">14</text>
	<text transform="matrix(1 0 0 1 23.50 65.50)">15</text>
	<text transform="matrix(1 0 0 1 24.50 75.50)">16</text>
	<text transform="matrix(1 0 0 1 24.25 87.50)">17</text>
	<text transform="matrix(1 0 0 1 63.50 11.50)">18</text>
	<text transform="matrix(1 0 0 1 49.50 30.50)">19</text>
	<text transform="matrix(1 0 0 1 57.50 23.50)">20</text>
	<text transform="matrix(1 0 0 1 64.50 34.13)">21</text>
	<text transform="matrix(1 0 0 1 64.50 43.50)">22</text>
	<text transform="matrix(1 0 0 1 64.50 54.50)">23</text>
	<text transform="matrix(1 0 0 1 63.50 66.50)">24</text>
	<text transform="matrix(1 0 0 1 46.75 83.50)">25</text>
	<text transform="matrix(1 0 0 1 55.50 84.50)">26</text>
	<text transform="matrix(1 0 0 1 64.75 81.50)">27</text>
	<text transform="matrix(1 0 0 1 73.75 78.50)">28</text>
</g>
</svg>
