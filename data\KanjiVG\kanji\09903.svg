<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09903" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09903" kvg:element="餃">
	<g id="kvg:09903-g1" kvg:element="⻞" kvg:variant="true" kvg:original="食" kvg:position="left" kvg:radical="general">
		<path id="kvg:09903-s1" kvg:type="㇒" d="M30.26,11.14c0.06,0.68,0.27,1.81-0.11,2.73c-2.43,6-9.43,17.75-19.35,26.59"/>
		<path id="kvg:09903-s2" kvg:type="㇔/㇏" d="M32.02,15.83c4.34,2.01,11.21,8.26,12.29,11.38"/>
		<path id="kvg:09903-s3" kvg:type="㇐" d="M23.25,32.66c0.36,0.15,1.02,0.19,1.39,0.15c2.3-0.25,9.87-2.17,12.1-2c0.6,0.05,0.96,0.07,1.27,0.14"/>
		<path id="kvg:09903-s4" kvg:type="㇑" d="M18.27,40.94c0.39,0.78,0.78,1.68,0.78,2.72c0,1.04-0.13,54.29,0,55.34"/>
		<path id="kvg:09903-s5" kvg:type="㇕" d="M19.19,43.07c2.2-0.13,17.91-2.67,19.92-2.83c1.67-0.13,2.74,1.45,2.61,2.22c-0.26,1.57-1.97,17.54-2.52,21.01"/>
		<path id="kvg:09903-s6" kvg:type="㇐a" d="M19.58,52.23c2.97,0,17.36-1.81,20.72-1.81"/>
		<path id="kvg:09903-s7" kvg:type="㇐a" d="M19.44,64.06C25.5,63.5,31.75,62,39.29,61.52"/>
		<path id="kvg:09903-s8" kvg:type="㇐c" d="M19.39,76.56c3.73-0.25,15.46-2.17,19.07-1.99c0.97,0.04,1.56,0.07,2.05,0.14"/>
		<path id="kvg:09903-s9" kvg:type="㇐c" d="M19.89,89.81c3.73-0.25,15.46-2.17,19.07-1.99c0.97,0.04,1.56,0.07,2.05,0.14"/>
	</g>
	<g id="kvg:09903-g2" kvg:element="交" kvg:position="right">
		<g id="kvg:09903-g3" kvg:element="亠" kvg:position="top">
			<path id="kvg:09903-s10" kvg:type="㇑a" d="M67.97,12.5c0.96,0.63,2.54,3.04,2.54,4.28c0,4.92-0.03,8.6-0.13,12.13"/>
			<path id="kvg:09903-s11" kvg:type="㇐" d="M49,31.18c1.11,0.06,3.65,0.43,4.73,0.34c9.64-0.85,29.25-2.88,36.78-3.12c1.84-0.06,2.77,0.03,4.14,0.43"/>
		</g>
		<g id="kvg:09903-g4" kvg:element="父" kvg:position="bottom">
			<path id="kvg:09903-s12" kvg:type="㇒" d="M60.94,37.09c0.27,0.81-0.02,2.72-0.65,4.04C57.76,46.39,53.18,52.1,48,56.7"/>
			<path id="kvg:09903-s13" kvg:type="㇔" d="M77.27,35.73c7.58,4.54,13.84,9.78,17.63,15.29"/>
			<path id="kvg:09903-s14" kvg:type="㇒" d="M80.65,46.91c0.38,0.86,0.41,2.33-0.12,3.9C75.71,65.19,60,85.5,44.25,94.9"/>
			<path id="kvg:09903-s15" kvg:type="㇏" d="M57.84,54.27c2.49,0.27,21.5,28.34,31.85,38.98c1.95,2,3.38,1.97,5.07,2.15"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09903" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 22.50 12.50)">1</text>
	<text transform="matrix(1 0 0 1 35.50 15.50)">2</text>
	<text transform="matrix(1 0 0 1 27.50 28.48)">3</text>
	<text transform="matrix(1 0 0 1 12.50 49.50)">4</text>
	<text transform="matrix(1 0 0 1 21.50 39.50)">5</text>
	<text transform="matrix(1 0 0 1 23.46 49.75)">6</text>
	<text transform="matrix(1 0 0 1 23.50 60.43)">7</text>
	<text transform="matrix(1 0 0 1 23.47 72.10)">8</text>
	<text transform="matrix(1 0 0 1 23.46 86.50)">9</text>
	<text transform="matrix(1 0 0 1 60.50 8.50)">10</text>
	<text transform="matrix(1 0 0 1 47.50 27.50)">11</text>
	<text transform="matrix(1 0 0 1 49.50 41.50)">12</text>
	<text transform="matrix(1 0 0 1 68.50 40.50)">13</text>
	<text transform="matrix(1 0 0 1 70.50 49.50)">14</text>
	<text transform="matrix(1 0 0 1 59.50 53.50)">15</text>
</g>
</svg>
