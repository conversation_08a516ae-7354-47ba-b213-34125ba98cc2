<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0971c" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0971c" kvg:element="霜">
	<g id="kvg:0971c-g1" kvg:element="雨" kvg:variant="true" kvg:position="top" kvg:radical="general">
		<path id="kvg:0971c-s1" kvg:type="㇐" d="M32.26,16.82c2.45,0.31,4.7,0.35,7.13,0.03C46.46,15.9,57.5,14.41,67,13.63c2.64-0.21,5.35-0.54,7.99-0.13"/>
		<path id="kvg:0971c-s2" kvg:type="㇔/㇑" d="M20.16,27.75c-0.07,5.44-0.69,11.47-1.14,16.97"/>
		<path id="kvg:0971c-s3" kvg:type="㇖b/㇆" d="M21.81,30.13C41.81,27.25,71.77,24,86.72,24c7.74,0,3.73,4.5-0.27,8.25"/>
		<path id="kvg:0971c-s4" kvg:type="㇑" d="M53.02,17.34c0.95,0.95,1.21,2.16,1.21,3.57c0,2.26,0.01,12.3,0.01,18.59c0,2.64,0,4.62,0,5.09"/>
		<path id="kvg:0971c-s5" kvg:type="㇔" d="M35.75,34c2.45,0.61,5.78,2.42,7.11,3.42"/>
		<path id="kvg:0971c-s6" kvg:type="㇔" d="M34.45,41.88c2.35,0.59,5.98,2.77,7.26,3.76"/>
		<path id="kvg:0971c-s7" kvg:type="㇔" d="M66.12,30.25c3.07,1.08,6.54,3.23,7.76,4.13"/>
		<path id="kvg:0971c-s8" kvg:type="㇔" d="M67.4,39.43c2.33,0.59,5.52,2.37,6.79,3.35"/>
	</g>
	<g id="kvg:0971c-g2" kvg:element="相" kvg:position="bottom" kvg:phon="相">
		<g id="kvg:0971c-g3" kvg:element="木" kvg:position="left">
			<path id="kvg:0971c-s9" kvg:type="㇐" d="M14.89,61.52c2.24,0.6,4.54,0.61,6.74,0.44c6.94-0.53,15.37-2.36,22.89-3.47c1.98-0.29,4.17-0.85,6.15-0.35"/>
			<path id="kvg:0971c-s10" kvg:type="㇑" d="M33.78,50.75c0.86,0.86,1.19,2,1.19,3.27c0,0.43,0.01,24.19,0.02,35.98c0.01,3.71-0.12,5.69-0.12,6.12"/>
			<path id="kvg:0971c-s11" kvg:type="㇒" d="M34.22,60.9c0,1.47-0.37,2.45-1.36,4.03c-4.73,7.57-8.61,12.95-15.53,19.96"/>
			<path id="kvg:0971c-s12" kvg:type="㇔" d="M37.78,67.27c4.09,1.29,7.69,4.41,10.82,8.08"/>
		</g>
		<g id="kvg:0971c-g4" kvg:element="目" kvg:position="right">
			<path id="kvg:0971c-s13" kvg:type="㇑" d="M58.68,53.7c0.87,0.87,1.04,2.3,1.04,3.48c0,0.88-0.08,20.98-0.06,32.08c0.01,3.88,0.02,6.59,0.06,6.9"/>
			<path id="kvg:0971c-s14" kvg:type="㇕a" d="M60.43,54.81c2.08-0.13,15.03-1.63,20.08-2.07c3.04-0.27,4.68,0.76,4.68,3.38c0,4.88-0.06,27.75-0.06,32.88c0,2.12,0.07,4.7,0.04,4.92"/>
			<path id="kvg:0971c-s15" kvg:type="㇐a" d="M60.89,65.87c4.73-0.2,19.32-1.63,23.17-1.63"/>
			<path id="kvg:0971c-s16" kvg:type="㇐a" d="M60.83,78.28c6.35-0.49,17.02-1.77,23.19-1.77"/>
			<path id="kvg:0971c-s17" kvg:type="㇐a" d="M61.34,92.1c7.04-0.6,15.29-1.35,22.41-1.57"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0971c" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 25.75 17.13)">1</text>
	<text transform="matrix(1 0 0 1 13.50 30.50)">2</text>
	<text transform="matrix(1 0 0 1 23.50 26.50)">3</text>
	<text transform="matrix(1 0 0 1 46.50 23.50)">4</text>
	<text transform="matrix(1 0 0 1 29.25 37.50)">5</text>
	<text transform="matrix(1 0 0 1 27.50 44.50)">6</text>
	<text transform="matrix(1 0 0 1 59.25 34.50)">7</text>
	<text transform="matrix(1 0 0 1 60.50 41.50)">8</text>
	<text transform="matrix(1 0 0 1 8.25 61.63)">9</text>
	<text transform="matrix(1 0 0 1 22.50 53.50)">10</text>
	<text transform="matrix(1 0 0 1 18.50 71.50)">11</text>
	<text transform="matrix(1 0 0 1 41.50 67.50)">12</text>
	<text transform="matrix(1 0 0 1 50.50 65.50)">13</text>
	<text transform="matrix(1 0 0 1 60.75 51.50)">14</text>
	<text transform="matrix(1 0 0 1 65.25 63.05)">15</text>
	<text transform="matrix(1 0 0 1 65.25 75.50)">16</text>
	<text transform="matrix(1 0 0 1 65.25 89.50)">17</text>
</g>
</svg>
