<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09f08" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09f08" kvg:element="鼈">
	<g id="kvg:09f08-g1" kvg:element="敝" kvg:position="top">
		<g id="kvg:09f08-g2" kvg:position="left">
			<g id="kvg:09f08-g3" kvg:element="小">
				<path id="kvg:09f08-s1" kvg:type="㇑" d="M31.43,11c0.39,0.23,1.22,1.75,1.3,2.22c0.08,0.47-0.05,10.58-0.13,13.51"/>
				<path id="kvg:09f08-s2" kvg:type="㇒" d="M25.27,15.15c0.03,0.24,0.07,0.62-0.06,0.97c-0.76,2.04-5.15,6.51-11.15,9.24"/>
				<path id="kvg:09f08-s3" kvg:type="㇔" d="M39.77,14.1c3.19,1.19,8.24,4.89,9.04,6.73"/>
			</g>
			<g id="kvg:09f08-g4" kvg:element="冂">
				<path id="kvg:09f08-s4" kvg:type="㇑" d="M18.31,26.49c0.45,0.28,1.4,2.07,1.49,2.62c0.09,0.55-0.06,15.18-0.15,18.64"/>
				<path id="kvg:09f08-s5" kvg:type="㇆a" d="M20.2,29.01c2.75-0.19,21.17-3.08,23.22-3.2c2.41-0.14,2.89,1.42,2.84,2.69c-0.31,7.77,0.24,10.49-0.68,16.75c-0.67,4.54-3.62,0.62-4.77-0.93"/>
			</g>
			<g id="kvg:09f08-g5" kvg:element="小" kvg:variant="true">
				<path id="kvg:09f08-s6" kvg:type="㇑" d="M32.46,29.77c0.02,0.12,0.33,0.64,0.35,1.43c0.11,3.76-0.07,14.13-0.07,15.41"/>
				<path id="kvg:09f08-s7" kvg:type="㇒" d="M27.48,33.61c0.01,0.25,0.03,0.63-0.02,0.99c-0.31,2.08-2.06,6.65-4.46,9.45"/>
				<path id="kvg:09f08-s8" kvg:type="㇔" d="M37.21,32.53c1.43,1.36,3.69,5.59,4.04,7.71"/>
			</g>
		</g>
		<g id="kvg:09f08-g6" kvg:element="攵" kvg:variant="true" kvg:original="攴" kvg:position="right">
			<g id="kvg:09f08-g7" kvg:element="𠂉" kvg:position="top">
				<path id="kvg:09f08-s9" kvg:type="㇒" d="M64.8,11.78c0.03,0.43,0.17,1.13-0.07,1.74C62.99,17.9,59.05,25.31,53,30.6"/>
				<path id="kvg:09f08-s10" kvg:type="㇐" d="M63.77,21.98c1,0.23,2.85,0,3.39-0.03c5.58-0.37,14.25-2.78,22.78-3.38c0.96-0.07,1.55-0.02,1.79,0.06"/>
			</g>
			<g id="kvg:09f08-g8" kvg:element="乂" kvg:position="bottom">
				<g id="kvg:09f08-g9" kvg:element="丿">
					<path id="kvg:09f08-s11" kvg:type="㇒" d="M77.79,23.15c-0.04,0.85,0.25,1.95-0.32,2.89c-5.86,9.62-10.2,14.85-22.67,20.8"/>
				</g>
				<path id="kvg:09f08-s12" kvg:type="㇏" d="M60.48,28.33c1.14,0,1.8,0.38,2.7,0.89c6.1,3.49,17.32,10.23,25.47,13.53c1.5,0.61,3.09,1.01,4.35,1.07"/>
			</g>
		</g>
	</g>
	<g id="kvg:09f08-g10" kvg:element="黽" kvg:variant="true" kvg:position="bottom" kvg:radical="general">
		<path id="kvg:09f08-s13" kvg:type="㇑" d="M24.72,52.55c0.66,0.7,0.92,1.11,1.05,2.06c0.48,3.64,0.98,5.64,1.69,9.47"/>
		<path id="kvg:09f08-s14" kvg:type="㇕" d="M25.54,53.33c9.32-1.24,51.88-4.4,57.79-5.23c2.17-0.31,3.67,0.14,3.17,2.35c-0.98,4.37-1.53,4.74-2.42,7.75"/>
		<path id="kvg:09f08-s15" kvg:type="㇐" d="M27.25,61.47c2.75,0,18.5-1.75,20.75-1.75"/>
		<path id="kvg:09f08-s16" kvg:type="㇐" d="M62.5,58.97c5-0.5,19.75-1.25,22.75-1.25"/>
		<path id="kvg:09f08-s17" kvg:type="㇑" d="M61.83,50.47c0.22,1,0,35.75-0.22,42"/>
		<path id="kvg:09f08-s18" kvg:type="㇟" d="M48.36,51.77c0,12.45-0.03,28.64-0.03,34.2c0,13,2.92,13.53,24.17,13.53c22.25,0,22.89-1.78,22.89-11.12"/>
		<path id="kvg:09f08-s19" kvg:type="㇒/㇑" d="M39.8,65.73c0.04,0.16,0.17,0.48-0.08,0.66c-3.34,2.33-7.24,4.42-14.86,6.27"/>
		<path id="kvg:09f08-s20" kvg:type="㇑/㇐" d="M22.53,71.05c0.62,0.44,1.37,1.92,1.52,2.83c0.56,3.29,2.32,16.17,2.88,20.62"/>
		<path id="kvg:09f08-s21" kvg:type="㇐" d="M25.48,80.38c3.05-0.2,14.47-2.5,15.2-2.5c0.73,0,1.74,0,2.32,0"/>
		<path id="kvg:09f08-s22" kvg:type="㇐" d="M27.23,91.38c3.05-0.2,13.47-2,14.2-2c0.73,0,1.74,0,2.32,0"/>
		<path id="kvg:09f08-s23" kvg:type="㇕" d="M62.54,66.66c9.71-0.91,14.24-1.09,20.08-2.04c2.15-0.35,3.28,0.9,3.13,2.14c-0.76,6.71-0.51,14.96-1.38,21.7"/>
		<path id="kvg:09f08-s24" kvg:type="㇐" d="M62.75,76.72c2.75,0,19.5-1.75,21.75-1.75"/>
		<path id="kvg:09f08-s25" kvg:type="㇐" d="M63,86.97c2.75,0,19-1.75,21.25-1.75"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09f08" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 25.50 9.50)">1</text>
	<text transform="matrix(1 0 0 1 17.50 15.55)">2</text>
	<text transform="matrix(1 0 0 1 37.50 11.50)">3</text>
	<text transform="matrix(1 0 0 1 12.50 34.50)">4</text>
	<text transform="matrix(1 0 0 1 23.25 25.50)">5</text>
	<text transform="matrix(1 0 0 1 26.25 43.25)">6</text>
	<text transform="matrix(1 0 0 1 22.25 35.50)">7</text>
	<text transform="matrix(1 0 0 1 39.25 34.10)">8</text>
	<text transform="matrix(1 0 0 1 57.50 11.50)">9</text>
	<text transform="matrix(1 0 0 1 68.50 18.50)">10</text>
	<text transform="matrix(1 0 0 1 66.50 28.50)">11</text>
	<text transform="matrix(1 0 0 1 57.50 35.50)">12</text>
	<text transform="matrix(1 0 0 1 15.50 58.50)">13</text>
	<text transform="matrix(1 0 0 1 23.50 49.50)">14</text>
	<text transform="matrix(1 0 0 1 29.50 59.58)">15</text>
	<text transform="matrix(1 0 0 1 65.50 56.50)">16</text>
	<text transform="matrix(1 0 0 1 52.00 58.10)">17</text>
	<text transform="matrix(1 0 0 1 38.75 58.93)">18</text>
	<text transform="matrix(1 0 0 1 27.50 67.78)">19</text>
	<text transform="matrix(1 0 0 1 11.75 79.70)">20</text>
	<text transform="matrix(1 0 0 1 31.50 77.50)">21</text>
	<text transform="matrix(1 0 0 1 31.50 87.50)">22</text>
	<text transform="matrix(1 0 0 1 65.50 64.50)">23</text>
	<text transform="matrix(1 0 0 1 65.50 74.50)">24</text>
	<text transform="matrix(1 0 0 1 65.50 84.38)">25</text>
</g>
</svg>
