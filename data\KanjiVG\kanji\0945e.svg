<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0945e" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0945e" kvg:element="鑞">
	<g id="kvg:0945e-g1" kvg:element="金" kvg:position="left" kvg:radical="general">
		<path id="kvg:0945e-s1" kvg:type="㇒" d="M29.75,15.24c0,0.78,0.05,1.4-0.16,2.33c-1.09,4.87-11.48,21.1-20.37,28.3"/>
		<path id="kvg:0945e-s2" kvg:type="㇔/㇏" d="M29.89,22.49c3.93,1.78,8.95,6.52,10.86,11.01"/>
		<path id="kvg:0945e-s3" kvg:type="㇐" d="M17.5,42.5c1.5,0,2.18,0.09,2.67,0.06C24.5,42.24,31,40.53,36.1,40.3c0.74-0.03,0.9-0.05,2.15-0.05"/>
		<path id="kvg:0945e-s4" kvg:type="㇐" d="M12.09,56.81c0.59,0.33,2.56,0.48,3.17,0.43c5.49-0.49,14.74-3.49,21.78-3.67c0.75-0.02,2.38-0.13,3.49,0.15"/>
		<path id="kvg:0945e-s5" kvg:type="㇑a" d="M25.95,43.69c1.24,0.78,1.24,2.52,1.24,3.14c0,4.35,0.31,35.17,0.31,40.48"/>
		<path id="kvg:0945e-s6" kvg:type="㇔" d="M14.14,66.51c3.34,5.32,4.97,11.08,5.65,14.19"/>
		<path id="kvg:0945e-s7" kvg:type="㇒" d="M39.2,62.55c0.3,0.84,0.46,2.06,0.34,2.76c-0.3,1.81-2.71,5.13-6.12,10.47"/>
		<path id="kvg:0945e-s8" kvg:type="㇀/㇐" d="M12.5,93.92c1.17,1.12,2.58,0.84,4.23,0.28c1.22-0.42,10.57-6.46,22.77-13.2"/>
	</g>
	<g id="kvg:0945e-g2" kvg:position="right">
		<g id="kvg:0945e-g3" kvg:element="巛">
			<path id="kvg:0945e-s9" kvg:type="㇛" d="M56.05,13c0.1,0.59,0.01,1.01-0.23,1.49c-1.62,3.2-3.71,7.48-7.8,10.87c-0.67,0.55-0.31,0.47,0,0.62c4.42,2.23,7.91,4.76,10.44,7.46"/>
			<path id="kvg:0945e-s10" kvg:type="㇛" d="M72.04,11.85c0.38,0.63-0.02,1.5-0.34,1.92c-1.64,2.19-2.96,4.98-7.88,9.51c-0.4,0.37-0.22,0.43,0.2,0.61c4.89,2.13,6.72,4.36,8.96,7.7"/>
			<path id="kvg:0945e-s11" kvg:type="㇛" d="M87.86,10.71c0.31,0.44,0.28,0.93,0.14,1.36c-0.77,2.42-3.12,5.92-8.18,9.62c-0.44,0.32-0.14,0.34,0.2,0.61c3.98,3.2,6.23,5.2,7.99,9.07"/>
		</g>
		<path id="kvg:0945e-s12" kvg:type="㇑" d="M48.82,37.12c0.17,0.27,0.47,0.92,0.57,1.26c0.79,2.8,1.82,10.6,2.31,15.82"/>
		<path id="kvg:0945e-s13" kvg:type="㇕" d="M49.34,38.2c6.1-0.7,35.67-2.9,40.22-3.49c1.67-0.22,2.53,0.55,2.45,1.32c-0.32,2.82-2.6,11.74-3.37,16.46"/>
		<g id="kvg:0945e-g4" kvg:element="乂">
			<g id="kvg:0945e-g5" kvg:element="丿">
				<path id="kvg:0945e-s14" kvg:type="㇒" d="M78.67,38.51c0.06,0.27,0.12,0.69-0.11,1.08c-1.32,2.27-8.89,7.26-19.25,10.31"/>
			</g>
			<path id="kvg:0945e-s15" kvg:type="㇏" d="M63.42,40.34c6.29,1.33,16.25,5.46,17.83,7.53"/>
		</g>
		<path id="kvg:0945e-s16" kvg:type="㇐" d="M51.72,52.95c4.39-0.3,29.97-2.47,36.92-2.61"/>
		<path id="kvg:0945e-s17" kvg:type="㇙" d="M48.37,57.07c0.71,0.38,1.14,1.73,1.28,2.5c0.13,0.7-0.08,27.5-0.08,34.31c0,1.81,0.87,1.73,1.69,1.14c2.88-2.06,7.13-5.22,11.94-7.93"/>
		<path id="kvg:0945e-s18" kvg:type="㇔" d="M56.21,61.1c2.31,0.98,5.98,4.03,6.56,5.55"/>
		<path id="kvg:0945e-s19" kvg:type="㇔" d="M55.54,74.63c2.31,1.11,5.98,4.57,6.56,6.3"/>
		<path id="kvg:0945e-s20" kvg:type="㇙" d="M67.84,56.97c0.71,0.38,1.13,1.73,1.28,2.5c0.13,0.7-0.08,28.01-0.08,34.82c0,1.81,0.87,1.73,1.69,1.14c2.88-2.06,9.24-7.47,12.44-9.35"/>
		<path id="kvg:0945e-s21" kvg:type="㇔" d="M76.09,61.18c2.23,1.16,5.75,4.76,6.31,6.55"/>
		<path id="kvg:0945e-s22" kvg:type="㇔" d="M76.05,75.67c2.18,1.11,5.64,4.55,6.18,6.27"/>
		<path id="kvg:0945e-s23" kvg:type="㇂" d="M86.41,55.75c0.69,0.86,1.04,1.09,1.11,2.93c0.51,12.71,1.47,29.57,9.79,36.5c3.87,3.23,3.53,1.37,2.93-7.2"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0945e" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 22.50 15.50)">1</text>
	<text transform="matrix(1 0 0 1 34.50 22.50)">2</text>
	<text transform="matrix(1 0 0 1 23.50 38.50)">3</text>
	<text transform="matrix(1 0 0 1 4.50 57.50)">4</text>
	<text transform="matrix(1 0 0 1 20.50 51.85)">5</text>
	<text transform="matrix(1 0 0 1 7.50 67.50)">6</text>
	<text transform="matrix(1 0 0 1 33.50 63.85)">7</text>
	<text transform="matrix(1 0 0 1 5.50 93.50)">8</text>
	<text transform="matrix(1 0 0 1 48.50 11.50)">9</text>
	<text transform="matrix(1 0 0 1 64.50 8.95)">10</text>
	<text transform="matrix(1 0 0 1 78.50 8.50)">11</text>
	<text transform="matrix(1 0 0 1 41.25 47.80)">12</text>
	<text transform="matrix(1 0 0 1 48.25 36.73)">13</text>
	<text transform="matrix(1 0 0 1 78.50 43.50)">14</text>
	<text transform="matrix(1 0 0 1 55.50 45.50)">15</text>
	<text transform="matrix(1 0 0 1 68.50 50.25)">16</text>
	<text transform="matrix(1 0 0 1 40.50 62.18)">17</text>
	<text transform="matrix(1 0 0 1 51.75 68.50)">18</text>
	<text transform="matrix(1 0 0 1 51.50 82.50)">19</text>
	<text transform="matrix(1 0 0 1 58.75 59.45)">20</text>
	<text transform="matrix(1 0 0 1 73.50 69.50)">21</text>
	<text transform="matrix(1 0 0 1 72.50 83.50)">22</text>
	<text transform="matrix(1 0 0 1 77.50 58.13)">23</text>
</g>
</svg>
