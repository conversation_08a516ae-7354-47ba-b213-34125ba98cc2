<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09b1f" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09b1f" kvg:element="鬟">
	<g id="kvg:09b1f-g1" kvg:element="髟" kvg:position="top" kvg:radical="general">
		<g id="kvg:09b1f-g2" kvg:element="長" kvg:variant="true" kvg:position="left">
			<path id="kvg:09b1f-s1" kvg:type="㇑a" d="M22.5,8.56c1.39,1.02,1.39,1.19,1.39,2.21s0.46,17.57,0.46,19.1"/>
			<path id="kvg:09b1f-s2" kvg:type="㇐b" d="M24.19,10.63c4.64-0.21,17.31-1.88,23.5-2.64c1.4-0.17,2.26,0.14,2.96,0.29"/>
			<path id="kvg:09b1f-s3" kvg:type="㇐b" d="M24.52,17.39c4.22-0.36,15.11-1.66,18.52-2.02c1.28-0.13,2.58-0.09,3.57,0.12"/>
			<path id="kvg:09b1f-s4" kvg:type="㇐b" d="M24.28,23.38c4.22-0.36,15.61-1.66,19.02-2.02c1.28-0.13,2.58-0.09,3.57,0.12"/>
			<path id="kvg:09b1f-s5" kvg:type="㇐" d="M9.88,32.02c0.79,0.21,2.25,0.24,3.05,0.21c8.27-0.35,25.49-3.72,36.77-3.99c1.32-0.03,2.12,0.1,2.78,0.2"/>
			<g id="kvg:09b1f-g3" kvg:element="厶">
				<path id="kvg:09b1f-s6" kvg:type="㇜" d="M27.23,32.62c0.07,0.51-0.35,1.25-0.59,1.51c-3.2,3.45-4.12,4.53-8.54,8.38c-1.1,0.97-0.53,2.57,1.36,2.09c7.29-1.85,17.29-4.35,25.59-6.75"/>
				<path id="kvg:09b1f-s7" kvg:type="㇔" d="M41.09,33.48c2.73,1.47,7.06,6.06,7.74,8.35"/>
			</g>
		</g>
		<g id="kvg:09b1f-g4" kvg:element="彡" kvg:position="right">
			<path id="kvg:09b1f-s8" kvg:type="㇒" d="M82.47,8.5c0.07,0.29-0.21,1.29-0.64,1.66C78.51,13,71.62,16.64,58.31,20.35"/>
			<path id="kvg:09b1f-s9" kvg:type="㇒" d="M87.59,16.36c0.09,0.38-0.1,1.56-0.67,2.01c-4.28,3.44-13.9,9.59-29.1,13.97"/>
			<path id="kvg:09b1f-s10" kvg:type="㇒" d="M93.05,25.22c0.1,0.4-0.13,1.56-0.69,2.09C88.46,30.98,76.73,37.5,57.93,42"/>
		</g>
	</g>
	<g id="kvg:09b1f-g5" kvg:element="睘" kvg:position="bottom">
		<g id="kvg:09b1f-g6" kvg:element="罒" kvg:variant="true" kvg:original="网">
			<path id="kvg:09b1f-s11" kvg:type="㇑" d="M27.87,48.52c0.19,0.18,0.38,0.64,0.46,0.86c0.65,1.77,2.22,6.02,2.66,9.2"/>
			<path id="kvg:09b1f-s12" kvg:type="㇕a" d="M29.43,49.09c5.47-0.71,49.44-3.57,51.63-3.58c1.69-0.01,2.44,1.24,2.16,3.09c-0.31,2.1-1.04,4.14-2.23,7.1"/>
			<path id="kvg:09b1f-s13" kvg:type="㇑a" d="M45.38,47.6c0.04,0.34,0.54,1.06,0.66,1.95c0.31,2.18,0.38,5.36,0.46,6.3"/>
			<path id="kvg:09b1f-s14" kvg:type="㇑a" d="M63.15,46.86c-0.03,0.5,0.3,1.21,0.22,2.01c-0.24,2.26-1.03,5.23-1.22,6.1"/>
			<path id="kvg:09b1f-s15" kvg:type="㇐a" d="M31.58,57.3c2.56-0.26,46.2-2.71,49.71-3.11"/>
		</g>
		<path id="kvg:09b1f-s16" kvg:type="㇐" d="M15.75,63.99c1.84,0.49,5.21,0.62,7.05,0.49c11.72-0.84,51.66-3.84,63.01-3.27c3.06,0.15,4.91,0.23,6.44,0.48"/>
		<g id="kvg:09b1f-g7" kvg:element="口">
			<path id="kvg:09b1f-s17" kvg:type="㇑" d="M34.25,69.56c0.26,0.18,0.52,0.33,0.64,0.55c0.9,1.77,1.97,6.32,2.58,9.49"/>
			<path id="kvg:09b1f-s18" kvg:type="㇕b" d="M35.86,70.82c7.39-0.57,33.26-2.7,36.52-2.95c1.19-0.09,2.41,1.42,2.24,1.96c-0.71,2.23-0.95,2.66-2.47,5.87"/>
			<path id="kvg:09b1f-s19" kvg:type="㇐b" d="M37.72,78.1c6.28-0.6,29.42-1.75,35.62-2.45"/>
		</g>
		<g id="kvg:09b1f-g8" kvg:element="𧘇">
			<path id="kvg:09b1f-s20" kvg:type="㇒" d="M49.89,77.25c0.09,0.39,0.37,1.04-0.19,1.57c-3.57,3.41-15.21,10.43-33.1,15"/>
			<path id="kvg:09b1f-s21" kvg:type="㇙/㇑" d="M41.79,87.23c0.39,0.24,0.81,0.47,0.81,0.88c-0.03,5.21-0.06,6.62-0.26,10.18c-0.09,1.5,0.41,2,2.41,1.09s11-5.29,13.25-6.38"/>
			<path id="kvg:09b1f-s22" kvg:type="㇒" d="M78.7,78.27c0.04,0.19,0.38,0.89,0.17,1.14c-1.34,1.53-5.54,3.7-12.37,6.74"/>
			<path id="kvg:09b1f-s23" kvg:type="㇏" d="M52.67,80.63c1.64,0.57,4.26,2.17,4.71,2.41c6.7,3.57,22.97,12.68,27.5,14.79c1.7,0.79,3.16,1.13,4.87,1.36"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09b1f" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 17.50 17.50)">1</text>
	<text transform="matrix(1 0 0 1 26.50 7.50)">2</text>
	<text transform="matrix(1 0 0 1 28.50 16.50)">3</text>
	<text transform="matrix(1 0 0 1 28.50 22.50)">4</text>
	<text transform="matrix(1 0 0 1 3.50 32.50)">5</text>
	<text transform="matrix(1 0 0 1 13.50 40.50)">6</text>
	<text transform="matrix(1 0 0 1 35.50 36.50)">7</text>
	<text transform="matrix(1 0 0 1 73.50 8.50)">8</text>
	<text transform="matrix(1 0 0 1 79.50 19.50)">9</text>
	<text transform="matrix(1 0 0 1 81.50 28.50)">10</text>
	<text transform="matrix(1 0 0 1 20.50 56.50)">11</text>
	<text transform="matrix(1 0 0 1 34.50 46.50)">12</text>
	<text transform="matrix(1 0 0 1 49.50 53.73)">13</text>
	<text transform="matrix(1 0 0 1 65.50 52.50)">14</text>
	<text transform="matrix(1 0 0 1 33.50 55.50)">15</text>
	<text transform="matrix(1 0 0 1 5.50 64.50)">16</text>
	<text transform="matrix(1 0 0 1 24.25 75.43)">17</text>
	<text transform="matrix(1 0 0 1 37.50 69.50)">18</text>
	<text transform="matrix(1 0 0 1 40.50 76.50)">19</text>
	<text transform="matrix(1 0 0 1 28.25 85.50)">20</text>
	<text transform="matrix(1 0 0 1 33.50 96.13)">21</text>
	<text transform="matrix(1 0 0 1 63.75 83.13)">22</text>
	<text transform="matrix(1 0 0 1 48.75 89.50)">23</text>
</g>
</svg>
