<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09e1e" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09e1e" kvg:element="鸞">
	<g id="kvg:09e1e-g1" kvg:element="䜌" kvg:position="top">
		<g id="kvg:09e1e-g2" kvg:element="言">
			<path id="kvg:09e1e-s1" kvg:type="㇔" d="M48.33,9.25c2.32,0.75,5.99,3.09,6.57,4.26"/>
			<path id="kvg:09e1e-s2" kvg:type="㇐" d="M41.31,18.44c0.54,0.14,2.61,0.16,3.16,0.14C51.17,18.33,60,17,63.94,16.37c0.89-0.14,2.27,0.07,2.72,0.14"/>
			<path id="kvg:09e1e-s3" kvg:type="㇐" d="M44.7,23.93c0.37,0.08,2.61,0.08,2.97,0.08c3.81-0.09,7.11-0.83,10.86-0.83c0.61,0,3.16,0.04,3.46,0.07"/>
			<path id="kvg:09e1e-s4" kvg:type="㇐" d="M44.78,30.17c0.33,0.06,2.35,0.07,2.68,0.06c3.43-0.07,8.52-1.12,11.9-1.12c0.55,0,2.84,0.03,3.12,0.06"/>
			<g id="kvg:09e1e-g3" kvg:element="口">
				<path id="kvg:09e1e-s5" kvg:type="㇑" d="M46.45,35.43c0.29,0.18,0.62,0.33,0.72,0.57c0.83,2.11,0.88,6.9,1.72,10"/>
				<path id="kvg:09e1e-s6" kvg:type="㇕b" d="M48.27,36.3c5.38-0.45,8-1.17,12.96-1.62c1.24-0.11,2,0.52,1.82,1.03c-0.74,2.13-1.39,4.59-2.7,8.13"/>
				<path id="kvg:09e1e-s7" kvg:type="㇐b" d="M49.49,44.89c4.01-0.27,7.1-0.37,12.6-0.79"/>
			</g>
		</g>
		<g id="kvg:09e1e-g4" kvg:element="糸">
			<path id="kvg:09e1e-s8" kvg:type="㇜" d="M25.9,8.25c0.31,0.99,0.56,1.82-0.16,2.82C24,13.5,21.03,16.68,17.92,19c-0.64,0.48-0.62,2.3,0,2.49c3.26,0.99,5.34,0.65,8.14,2.48"/>
			<path id="kvg:09e1e-s9" kvg:type="㇜" d="M34.53,15.1c0.22,0.35,0.29,1.5,0,1.87c-4.31,5.5-11.57,10-17.49,15.01c-1.36,1.15,0.34,1.81,1.22,1.57c3.7-0.98,13.39-3.55,18.15-4.48"/>
			<path id="kvg:09e1e-s10" kvg:type="㇔" d="M33.75,25.18c1.85,1.47,4.79,6.04,5.25,8.32"/>
			<path id="kvg:09e1e-s11" kvg:type="㇑" d="M26.33,33.55c0.05,0.23,0.73,1.21,0.78,2.68c0.25,7.08-0.16,12.6-0.16,15.01"/>
			<path id="kvg:09e1e-s12" kvg:type="㇒" d="M19.99,37.87c0.13,0.49,0.11,1.47-0.13,1.82c-1.6,2.31-5.6,6.75-7.86,8.73"/>
			<path id="kvg:09e1e-s13" kvg:type="㇔" d="M34.25,37.08c2.62,2.13,4.64,7.08,5.25,9.01"/>
		</g>
		<g id="kvg:09e1e-g5" kvg:element="糸">
			<path id="kvg:09e1e-s14" kvg:type="㇜" d="M80.87,8c0.26,0.71,0.39,1.25-0.13,2c-2.49,3.57-2.99,5.46-7.43,9.68c-0.44,0.42-0.52,1.63,0,1.77c2.74,0.71,5.44,1.55,8.1,2.18"/>
			<path id="kvg:09e1e-s15" kvg:type="㇜" d="M90.29,14.04c0.22,0.32,0.29,1.35,0,1.69C85.97,20.67,79.14,27.3,73.2,31.82c-1.36,1.04,0.34,1.63,1.22,1.42c3.71-0.88,13.11-2.38,17.88-3.22"/>
			<path id="kvg:09e1e-s16" kvg:type="㇔" d="M89.62,24.84c1.92,1.65,4.95,6.76,5.42,9.32"/>
			<path id="kvg:09e1e-s17" kvg:type="㇑" d="M81.14,34.41c0.04,0.17,0.61,0.86,0.66,1.91c0.21,5.03-0.13,12.72-0.13,14.43"/>
			<path id="kvg:09e1e-s18" kvg:type="㇒" d="M73.29,38.6c0.11,0.42,0.09,1.25-0.11,1.54c-1.27,1.96-4.44,5.73-6.24,7.41"/>
			<path id="kvg:09e1e-s19" kvg:type="㇔" d="M88.84,38.14c2.9,1.89,5.12,6.28,5.79,8"/>
		</g>
	</g>
	<g id="kvg:09e1e-g6" kvg:element="鳥" kvg:position="bottom" kvg:radical="general">
		<path id="kvg:09e1e-s20" kvg:type="㇒" d="M52.23,49.18c0.02,0.14,0.03,0.36-0.03,0.55c-0.39,1.16-2.63,3.72-5.7,5.28"/>
		<path id="kvg:09e1e-s21" kvg:type="㇑" d="M32.13,55.17c0.46,0.46,0.82,1.1,0.82,1.89c0,8.11,0.05,13.75-0.23,26.52"/>
		<path id="kvg:09e1e-s22" kvg:type="㇕a" d="M34.02,57.85c2.13-0.06,38.09-2.9,40.17-3.01c1.82-0.09,3.57,1.04,3.29,2.07C77.31,57.54,77,65,75.75,69.39"/>
		<path id="kvg:09e1e-s23" kvg:type="㇐a" d="M34.36,63.46c2.61,0,39.42-2.3,42.37-2.3"/>
		<path id="kvg:09e1e-s24" kvg:type="㇐a" d="M34.24,69.74c5.31-0.22,34.87-1.5,41.48-1.7"/>
		<path id="kvg:09e1e-s25" kvg:type="㇐b" d="M33.03,76.62c13.63-0.77,45.46-2.87,51.31-3.29c2.04-0.15,5.5-0.32,6.54-0.21"/>
		<path id="kvg:09e1e-s26" kvg:type="㇆a" d="M33.23,83.45c12.48-1.24,43.35-2.84,50.27-3.29c4.5-0.3,5.75,1.39,5,4.91c-1,4.69-2.25,7.44-5,12.82c-2.22,4.35-5.75,2.37-8.5-0.85"/>
		<g id="kvg:09e1e-g7" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09e1e-s27" kvg:type="㇔" d="M24.21,86.66c-0.46,5.59-2.21,8.09-6.25,12.59"/>
			<path id="kvg:09e1e-s28" kvg:type="㇔" d="M37.25,87.95c1.21,1.87,2.36,7.02,2.66,9.94"/>
			<path id="kvg:09e1e-s29" kvg:type="㇔" d="M50.32,86.69c1.36,1.61,3.52,6.64,3.86,9.15"/>
			<path id="kvg:09e1e-s30" kvg:type="㇔" d="M64.5,85.79c1.68,1.38,4.33,5.67,4.75,7.81"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09e1e" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 42.75 8.50)">1</text>
	<text transform="matrix(1 0 0 1 39.50 16.50)">2</text>
	<text transform="matrix(1 0 0 1 39.50 25.50)">3</text>
	<text transform="matrix(1 0 0 1 39.50 31.50)">4</text>
	<text transform="matrix(1 0 0 1 42.50 41.50)">5</text>
	<text transform="matrix(1 0 0 1 48.00 35.50)">6</text>
	<text transform="matrix(1 0 0 1 50.75 43.50)">7</text>
	<text transform="matrix(1 0 0 1 19.50 8.50)">8</text>
	<text transform="matrix(1 0 0 1 30.50 13.50)">9</text>
	<text transform="matrix(1 0 0 1 32.50 24.50)">10</text>
	<text transform="matrix(1 0 0 1 20.50 45.50)">11</text>
	<text transform="matrix(1 0 0 1 9.50 42.50)">12</text>
	<text transform="matrix(1 0 0 1 29.50 45.50)">13</text>
	<text transform="matrix(1 0 0 1 70.50 8.50)">14</text>
	<text transform="matrix(1 0 0 1 84.50 11.50)">15</text>
	<text transform="matrix(1 0 0 1 90.75 26.50)">16</text>
	<text transform="matrix(1 0 0 1 73.00 47.50)">17</text>
	<text transform="matrix(1 0 0 1 64.50 40.50)">18</text>
	<text transform="matrix(1 0 0 1 93.50 42.50)">19</text>
	<text transform="matrix(1 0 0 1 57.25 50.20)">20</text>
	<text transform="matrix(1 0 0 1 24.50 63.13)">21</text>
	<text transform="matrix(1 0 0 1 36.75 54.50)">22</text>
	<text transform="matrix(1 0 0 1 37.49 62.50)">23</text>
	<text transform="matrix(1 0 0 1 37.34 68.50)">24</text>
	<text transform="matrix(1 0 0 1 37.50 75.50)">25</text>
	<text transform="matrix(1 0 0 1 37.38 81.50)">26</text>
	<text transform="matrix(1 0 0 1 14.50 90.50)">27</text>
	<text transform="matrix(1 0 0 1 28.50 93.50)">28</text>
	<text transform="matrix(1 0 0 1 42.75 93.50)">29</text>
	<text transform="matrix(1 0 0 1 56.50 92.50)">30</text>
</g>
</svg>
