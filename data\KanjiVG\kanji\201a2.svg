<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_201a2" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:201a2" kvg:element="𠆢" kvg:variant="true" kvg:original="人" kvg:radical="general">
	<path id="kvg:201a2-s1" kvg:type="㇒" d="M 51.75,30.648068 c 0.11,1.16 -0.12,2.81 -0.73,4.43 -3.77,10.07 -19.27,32.32 -40.02,44.82"/>
	<path id="kvg:201a2-s2" kvg:type="㇏" d="M 52,36.148068 c 8.62,9.25 22.88,23.5 35.34,34.33 2.86,2.48 5.66,4.55 8.91,6.17"/>
</g>
</g>
<g id="kvg:StrokeNumbers_201a2" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 43.50 33.28)">1</text>
	<text transform="matrix(1 0 0 1 60.50 40.78)">2</text>
</g>
</svg>
