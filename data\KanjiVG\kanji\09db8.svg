<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09db8" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09db8" kvg:element="鶸">
	<g id="kvg:09db8-g1" kvg:element="弱" kvg:variant="true" kvg:position="left">
		<g id="kvg:09db8-g2" kvg:element="弓" kvg:position="left">
			<path id="kvg:09db8-s1" kvg:type="㇕c" d="M10.85,21.86c0.82,0.56,2.61,0.52,3.42,0.42c4.92-0.57,8.6-2.7,11.55-3.41c1.63-0.39,2.49-0.24,2.02,1.57c-0.48,1.81-1.76,9.89-2.91,13.81"/>
			<path id="kvg:09db8-s2" kvg:type="㇐" d="M15,36.72c1.31,0,8.19-1.39,9.13-1.39s1.87-0.35,2.62-0.35"/>
			<path id="kvg:09db8-s3" kvg:type="㇉" d="M14.33,35.64c0.42,1.11,0.21,2.96-0.03,4.39c-0.27,1.64-2.02,6.95-2.68,10.51c-0.45,2.42,0.21,2.59,1.36,2.25c4.27-1.29,11.34-2.69,13.35-2.84c1.64-0.13,2.46,1.19,2.3,4.3c-0.51,9.82-0.63,21.5-3.23,33.52c-0.89,4.1-3.9,6.48-7.21,3.07"/>
		</g>
		<g id="kvg:09db8-g3" kvg:position="right">
			<g id="kvg:09db8-g4" kvg:element="冫" kvg:variant="true">
				<g id="kvg:09db8-g5" kvg:position="top">
					<path id="kvg:09db8-s4" kvg:type="㇒" d="M21.16,56.42c0.03,0.35,0.13,0.95-0.06,1.4c-1.8,4.31-5.05,8.75-9.8,13.39"/>
				</g>
				<g id="kvg:09db8-g6" kvg:position="bottom">
					<path id="kvg:09db8-s5" kvg:type="㇒" d="M25.55,66.23c0.04,0.43,0.22,1.18-0.08,1.73c-2.61,4.74-8.18,11.33-14.48,16.53"/>
				</g>
			</g>
			<g id="kvg:09db8-g7" kvg:element="弓">
				<path id="kvg:09db8-s6" kvg:type="㇕c" d="M34.1,20.03c1.35,0.79,2.62,0.58,3.42,0.42c2.83-0.57,8.91-2.3,10.5-2.98c1.58-0.68,2.49-0.24,2.02,1.57c-0.47,1.81-2.12,7.32-3.56,14.27"/>
				<path id="kvg:09db8-s7" kvg:type="㇐" d="M37.5,35.64c1.26,0,7.84-1.62,8.74-1.62s1.79-0.41,2.51-0.41"/>
				<path id="kvg:09db8-s8" kvg:type="㇉" d="M36.62,34.21c0.37,0.91,0.71,1.46,0.47,2.89c-0.27,1.64-1.86,6.56-2.65,11.04c-0.2,1.11,0.24,2.64,1.36,2.25c4.7-1.64,9.85-3.22,11.87-3.38c1.64-0.13,2.45,1.2,2.3,4.3C49.25,66.5,49,77.25,46.28,88.7c-0.96,4.05-4.28,2.8-6.35,1.6"/>
			</g>
			<g id="kvg:09db8-g8" kvg:element="冫" kvg:variant="true">
				<g id="kvg:09db8-g9" kvg:position="top">
					<path id="kvg:09db8-s9" kvg:type="㇒" d="M42.28,54.05c0.03,0.34,0.06,0.88-0.05,1.37c-0.65,2.89-3.98,7.83-9.42,13.12"/>
				</g>
				<g id="kvg:09db8-g10" kvg:position="bottom">
					<path id="kvg:09db8-s10" kvg:type="㇒" d="M47.05,64.8c0.04,0.43,0.18,1.17-0.09,1.74c-2.35,5.01-7.93,11.75-15.39,17.14"/>
				</g>
			</g>
		</g>
	</g>
	<g id="kvg:09db8-g11" kvg:element="鳥" kvg:position="right" kvg:radical="general">
		<path id="kvg:09db8-s11" kvg:type="㇒" d="M70.54,11.91c0.02,0.27,0.04,0.7-0.03,1.07c-0.41,2.24-2.74,7.02-5.93,9.81"/>
		<path id="kvg:09db8-s12" kvg:type="㇑" d="M59.3,24.25c0.33,0.42,0.6,0.98,0.6,1.67c0,6.97,0.04,32.75-0.17,43.71"/>
		<path id="kvg:09db8-s13" kvg:type="㇕a" d="M60.31,25.52c1.55,0,22-3.05,23.51-2.95c2.24,0.15,3.33,2.07,3.13,3.87c-0.12,1.11-2.64,12.21-4.08,20.67"/>
		<path id="kvg:09db8-s14" kvg:type="㇐a" d="M60.56,35.92c1.9,0.13,21.6-2.64,23.75-2.5"/>
		<path id="kvg:09db8-s15" kvg:type="㇐a" d="M60.47,48.03c6.03-0.78,17.55-2.9,22.38-2.91"/>
		<path id="kvg:09db8-s16" kvg:type="㇐b" d="M60.69,58.76c8.73-1.07,28.04-3.61,31.79-4.28c1.31-0.23,3.53-0.44,4.18-0.13"/>
		<path id="kvg:09db8-s17" kvg:type="㇆a" d="M59.74,69.83c9.1-1.57,26.13-3.79,31.17-4.26c3.28-0.3,4.97,0.86,4.38,4.41c-1.64,9.87-4.08,19.24-6.93,25.3c-2.86,6.1-5.97,1.01-7.3-0.23"/>
		<g id="kvg:09db8-g12" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09db8-s18" kvg:type="㇔" d="M55.77,80.37c0.47,3.93-0.34,8.65-1.73,11.06"/>
			<path id="kvg:09db8-s19" kvg:type="㇔" d="M62.57,78.44c1.5,2.02,2.92,7.32,3.29,10.28"/>
			<path id="kvg:09db8-s20" kvg:type="㇔" d="M71.24,75.79c1.84,1.77,4.76,7.12,5.22,9.73"/>
			<path id="kvg:09db8-s21" kvg:type="㇔" d="M80.41,74.07c1.8,1.62,4.66,6.5,5.11,8.87"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09db8" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 4.50 21.50)">1</text>
	<text transform="matrix(1 0 0 1 13.50 33.50)">2</text>
	<text transform="matrix(1 0 0 1 6.50 42.50)">3</text>
	<text transform="matrix(1 0 0 1 12.50 61.50)">4</text>
	<text transform="matrix(1 0 0 1 17.50 71.50)">5</text>
	<text transform="matrix(1 0 0 1 31.50 17.50)">6</text>
	<text transform="matrix(1 0 0 1 34.50 31.50)">7</text>
	<text transform="matrix(1 0 0 1 29.50 41.10)">8</text>
	<text transform="matrix(1 0 0 1 33.50 59.50)">9</text>
	<text transform="matrix(1 0 0 1 36.50 71.95)">10</text>
	<text transform="matrix(1 0 0 1 61.25 9.88)">11</text>
	<text transform="matrix(1 0 0 1 50.50 30.50)">12</text>
	<text transform="matrix(1 0 0 1 57.50 22.50)">13</text>
	<text transform="matrix(1 0 0 1 63.50 33.50)">14</text>
	<text transform="matrix(1 0 0 1 63.50 44.50)">15</text>
	<text transform="matrix(1 0 0 1 63.50 55.50)">16</text>
	<text transform="matrix(1 0 0 1 63.50 66.50)">17</text>
	<text transform="matrix(1 0 0 1 49.50 78.35)">18</text>
	<text transform="matrix(1 0 0 1 56.50 87.50)">19</text>
	<text transform="matrix(1 0 0 1 67.50 84.50)">20</text>
	<text transform="matrix(1 0 0 1 76.50 81.50)">21</text>
</g>
</svg>
