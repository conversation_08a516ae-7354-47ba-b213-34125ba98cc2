<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09d3e" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09d3e" kvg:element="鴾">
	<g id="kvg:09d3e-g1" kvg:element="牟" kvg:position="left">
		<g id="kvg:09d3e-g2" kvg:element="厶" kvg:position="top">
			<path id="kvg:09d3e-s1" kvg:type="㇜" d="M30.78,11c0.32,0.46-0.08,2.18-0.32,2.72c-3.21,7.26-11.28,14.38-15.7,19.66c-1.4,1.67-1.38,2.25,0.65,1.67c5.47-1.57,22.34-5.11,29.69-6.56"/>
			<path id="kvg:09d3e-s2" kvg:type="㇔" d="M39.88,21.85c3.12,2.07,8.05,8.5,8.83,11.72"/>
		</g>
		<g id="kvg:09d3e-g3" kvg:element="牛" kvg:position="bottom">
			<path id="kvg:09d3e-s3" kvg:type="㇒" d="M21.56,44.79c0.02,0.45,0.04,1.15-0.04,1.8c-0.48,3.79-3.22,12.1-6.97,17.19"/>
			<path id="kvg:09d3e-s4" kvg:type="㇐" d="M22.47,54.44c0.61,0.34,1.74,0.4,2.35,0.34c5.43-0.53,11.43-1.78,18.96-3.18c1.01-0.19,1.64,0.16,2.15,0.33"/>
			<path id="kvg:09d3e-s5" kvg:type="㇐" d="M11.03,70.74c0.99,0.48,3.71,0.61,4.69,0.48c7.28-0.97,18.53-2.97,30.53-3.98c1.65-0.14,2.65,0.23,3.47,0.47"/>
			<path id="kvg:09d3e-s6" kvg:type="㇑" d="M31.02,37.22c0.5,0.4,1.32,1.44,1.32,3.78c0,4.33-0.06,49.26-0.16,54.25"/>
		</g>
	</g>
	<g id="kvg:09d3e-g4" kvg:element="鳥" kvg:position="right" kvg:radical="general">
		<path id="kvg:09d3e-s7" kvg:type="㇒" d="M69.99,10.16c0.02,0.3,0.04,0.79-0.03,1.22c-0.42,2.53-2.81,7.95-6.08,11.1"/>
		<path id="kvg:09d3e-s8" kvg:type="㇑" d="M57.34,23.93c0.36,0.42,0.64,0.98,0.64,1.66c0,6.93,0.04,34.05-0.18,44.95"/>
		<path id="kvg:09d3e-s9" kvg:type="㇕a" d="M58.42,25.2c1.66,0,21.43-3.03,23.04-2.93c2.39,0.15,3.56,2.06,3.34,3.85c-0.13,1.1-1.25,12.64-2.79,21.05"/>
		<path id="kvg:09d3e-s10" kvg:type="㇐a" d="M58.69,37.02c2.03,0.13,22.57-3.13,24.87-2.98"/>
		<path id="kvg:09d3e-s11" kvg:type="㇐a" d="M58.59,48.08c4.14-0.13,18.24-2.38,23.4-2.39"/>
		<path id="kvg:09d3e-s12" kvg:type="㇐b" d="M58.33,59.21c9.04-1.11,29.05-3.76,32.93-4.46c1.35-0.24,3.65-0.46,4.33-0.14"/>
		<path id="kvg:09d3e-s13" kvg:type="㇆a" d="M57.82,70.75c9.72-1.56,28.4-4.27,33.79-4.74c3.51-0.3,5.31,0.86,4.68,4.38c-1.75,9.81-3.36,18.62-6.4,24.66c-3.06,6.06-6.38,1-7.79-0.23"/>
		<g id="kvg:09d3e-g5" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09d3e-s14" kvg:type="㇔" d="M53.72,79.46c0.64,4.71-1.46,10.37-3.34,13.25"/>
			<path id="kvg:09d3e-s15" kvg:type="㇔" d="M61.72,77.79c2.08,2.11,4.05,7.64,4.57,10.73"/>
			<path id="kvg:09d3e-s16" kvg:type="㇔" d="M72.21,76.16c1.7,1.86,4.38,7.45,4.8,10.19"/>
			<path id="kvg:09d3e-s17" kvg:type="㇔" d="M81.24,73.7c1.92,1.84,4.96,7.38,5.44,10.08"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09d3e" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 23.50 9.50)">1</text>
	<text transform="matrix(1 0 0 1 41.50 19.50)">2</text>
	<text transform="matrix(1 0 0 1 13.25 46.50)">3</text>
	<text transform="matrix(1 0 0 1 25.25 51.50)">4</text>
	<text transform="matrix(1 0 0 1 4.50 72.33)">5</text>
	<text transform="matrix(1 0 0 1 36.50 39.25)">6</text>
	<text transform="matrix(1 0 0 1 61.50 9.50)">7</text>
	<text transform="matrix(1 0 0 1 49.50 26.50)">8</text>
	<text transform="matrix(1 0 0 1 58.50 21.50)">9</text>
	<text transform="matrix(1 0 0 1 62.14 33.95)">10</text>
	<text transform="matrix(1 0 0 1 62.37 44.50)">11</text>
	<text transform="matrix(1 0 0 1 62.21 55.50)">12</text>
	<text transform="matrix(1 0 0 1 62.25 66.73)">13</text>
	<text transform="matrix(1 0 0 1 44.50 80.65)">14</text>
	<text transform="matrix(1 0 0 1 55.50 85.58)">15</text>
	<text transform="matrix(1 0 0 1 65.50 81.50)">16</text>
	<text transform="matrix(1 0 0 1 73.50 77.50)">17</text>
</g>
</svg>
