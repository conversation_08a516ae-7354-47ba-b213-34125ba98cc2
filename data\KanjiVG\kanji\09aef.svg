<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09aef" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09aef" kvg:element="髯">
	<g id="kvg:09aef-g1" kvg:element="髟" kvg:position="top" kvg:radical="general">
		<g id="kvg:09aef-g2" kvg:element="長" kvg:variant="true" kvg:position="left">
			<path id="kvg:09aef-s1" kvg:type="㇑a" d="M23.5,10.84c1.39,0.92,1.39,1.57,1.39,2.49s0.46,20.88,0.46,22.26"/>
			<path id="kvg:09aef-s2" kvg:type="㇐b" d="M25.44,12.85C32.5,12,39.75,11,47.1,10.78c1.45-0.04,2.32,0.11,3.05,0.23"/>
			<path id="kvg:09aef-s3" kvg:type="㇐b" d="M25.02,20.29c4.22-0.32,14.61-2,18.02-2.32c1.28-0.12,2.58-0.08,3.57,0.11"/>
			<path id="kvg:09aef-s4" kvg:type="㇐b" d="M25.28,27.94c4.22-0.32,15.11-2,18.52-2.32c1.28-0.12,2.58-0.08,3.57,0.11"/>
			<path id="kvg:09aef-s5" kvg:type="㇐" d="M11.88,36.45c1.04,0.22,2.95,0.31,3.98,0.22C24,36,44.25,33,50.84,32.4c1.72-0.16,2.77,0.11,3.63,0.22"/>
			<g id="kvg:09aef-g3" kvg:element="厶">
				<path id="kvg:09aef-s6" kvg:type="㇜" d="M28.23,37.32c0.07,0.55-0.35,1.36-0.59,1.64c-3.2,3.75-4.12,4.92-8.54,9.11c-1.1,1.05-0.53,2.8,1.36,2.28c7.29-2.02,15.29-4.24,23.59-6.85"/>
				<path id="kvg:09aef-s7" kvg:type="㇔" d="M39.34,38.54c2.82,1.85,7.28,7.6,7.99,10.47"/>
			</g>
		</g>
		<g id="kvg:09aef-g4" kvg:element="彡" kvg:position="right">
			<path id="kvg:09aef-s8" kvg:type="㇒" d="M82.48,9c0.06,0.34-0.22,1.44-0.63,1.88c-3.16,3.38-9.67,7.78-22.29,12.19"/>
			<path id="kvg:09aef-s9" kvg:type="㇒" d="M88.09,19.28c0.09,0.4-0.11,1.61-0.66,2.09C83.26,25,73.9,31.5,59.08,36.11"/>
			<path id="kvg:09aef-s10" kvg:type="㇒" d="M93.07,30.79c0.09,0.4-0.17,1.57-0.67,2.11c-3.48,3.71-13.91,10.3-30.71,14.85"/>
		</g>
	</g>
	<g id="kvg:09aef-g5" kvg:element="冉" kvg:position="bottom">
		<g id="kvg:09aef-g6" kvg:element="冂">
			<path id="kvg:09aef-s11" kvg:type="㇑" d="M29.95,59.19c0.53,0.76,0.88,1.99,0.71,3.36c-0.18,1.38,0.18,35.41,0.18,37.7"/>
			<path id="kvg:09aef-s12" kvg:type="㇆a" d="M31.15,60.89c6.71-0.88,43.48-4.7,44.82-4.91c1.52-0.23,3.4,1.61,3.09,2.64c-0.53,1.76-0.53,30.04-0.53,35.11c0,8.16-5.29,3.01-7.62,1.32"/>
		</g>
		<g id="kvg:09aef-g7" kvg:element="土" kvg:variant="true">
			<path id="kvg:09aef-s13" kvg:type="㇑a" d="M51.92,49.12c0.12,0.29,1.77,1.52,1.77,3.39c0,5.77,0.14,20.73,0.14,28.36"/>
			<path id="kvg:09aef-s14" kvg:type="㇐a" d="M30.89,71.99c12.42-1.25,32.35-3.03,45.8-4.02"/>
			<path id="kvg:09aef-s15" kvg:type="㇐" d="M11.88,83.91c1.64,0.58,4.65,0.72,6.29,0.58c20.32-1.64,56.54-5.42,74.71-6.19c2.73-0.11,4.38,0.28,5.75,0.57"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09aef" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 18.85 20.50)">1</text>
	<text transform="matrix(1 0 0 1 27.66 9.50)">2</text>
	<text transform="matrix(1 0 0 1 29.50 18.50)">3</text>
	<text transform="matrix(1 0 0 1 29.50 25.50)">4</text>
	<text transform="matrix(1 0 0 1 5.50 36.50)">5</text>
	<text transform="matrix(1 0 0 1 15.50 45.50)">6</text>
	<text transform="matrix(1 0 0 1 34.50 41.50)">7</text>
	<text transform="matrix(1 0 0 1 74.60 9.50)">8</text>
	<text transform="matrix(1 0 0 1 79.50 21.50)">9</text>
	<text transform="matrix(1 0 0 1 82.50 32.50)">10</text>
	<text transform="matrix(1 0 0 1 20.50 65.50)">11</text>
	<text transform="matrix(1 0 0 1 31.50 58.50)">12</text>
	<text transform="matrix(1 0 0 1 50.50 46.50)">13</text>
	<text transform="matrix(1 0 0 1 34.50 69.50)">14</text>
	<text transform="matrix(1 0 0 1 2.50 84.50)">15</text>
</g>
</svg>
