<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09d1f" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09d1f" kvg:element="鴟">
	<g id="kvg:09d1f-g1" kvg:position="left">
		<g id="kvg:09d1f-g2" kvg:element="氏" kvg:position="top">
			<path id="kvg:09d1f-s1" kvg:type="㇒" d="M39.84,11.11c0.06,0.36,0.13,0.93-0.13,1.45c-1.56,3.06-8.87,9.09-21.02,14.23"/>
			<path id="kvg:09d1f-s2" kvg:type="㇙" d="M14,25.95c1,0.8,1.57,3.07,1.57,5.04c0,1.97-0.17,38.44-0.17,41.51c0,3.07-0.19,3.5,2.4,1.75c4.45-3,14.06-11.17,15.43-12.48"/>
			<path id="kvg:09d1f-s3" kvg:type="㇐" d="M15.87,43.59c0.53,0.15,1.51-0.11,2.03-0.21c7.81-1.46,20.8-3.06,27.5-4.11c0.87-0.14,1.41,0.07,1.85,0.15"/>
			<path id="kvg:09d1f-s4" kvg:type="㇂" d="M30.5,23.82c0.28,0.43,0.87,2.61,0.87,3.45c0.13,20.48,2.88,44.98,12.72,57.83c3.17,4.14,2.45-4.08,2.45-6.72"/>
		</g>
		<g id="kvg:09d1f-g3" kvg:element="一" kvg:position="bottom">
			<path id="kvg:09d1f-s5" kvg:type="㇐" d="M11.59,86.54c0.65,0.26,1.83,0.33,2.48,0.26c4.12-0.45,15.81-2.02,20.26-1.92c1.08,0.02,1.72,0.12,2.26,0.26"/>
		</g>
	</g>
	<g id="kvg:09d1f-g4" kvg:element="鳥" kvg:position="right" kvg:radical="general">
		<path id="kvg:09d1f-s6" kvg:type="㇒" d="M67.54,10.66c0.02,0.28,0.04,0.72-0.03,1.11c-0.41,2.32-2.74,7.29-5.93,10.18"/>
		<path id="kvg:09d1f-s7" kvg:type="㇑" d="M56.3,22.92c0.33,0.43,0.6,1.52,0.6,2.23c0,7.23,0.04,32.01-0.17,43.38"/>
		<path id="kvg:09d1f-s8" kvg:type="㇕a" d="M57.31,24.75c1.55,0,21.5-3.17,23.01-3.06c2.24,0.16,3.33,2.15,3.13,4.02c-0.12,1.15-1.64,11.69-3.08,20.46"/>
		<path id="kvg:09d1f-s9" kvg:type="㇐a" d="M57.56,35.08c1.9,0.13,22.1-2.74,24.25-2.59"/>
		<path id="kvg:09d1f-s10" kvg:type="㇐a" d="M57.47,46.12c3.88-0.14,18.05-2.48,22.88-2.5"/>
		<path id="kvg:09d1f-s11" kvg:type="㇐b" d="M57.69,57.36c8.67-0.95,28.35-3.76,32.07-4.35c1.3-0.21,3.5-0.39,4.16-0.12"/>
		<path id="kvg:09d1f-s12" kvg:type="㇆a" d="M56.74,68.75c9.1-1.63,28.63-3.92,33.67-4.4c3.28-0.31,4.97,0.89,4.38,4.57c-1.64,10.24-4.08,18.46-6.93,24.75c-2.86,6.33-5.97,1.04-7.3-0.24"/>
		<g id="kvg:09d1f-g5" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09d1f-s13" kvg:type="㇔" d="M55.43,81.98c0.5,3.62-0.36,7.97-1.82,10.19"/>
			<path id="kvg:09d1f-s14" kvg:type="㇔" d="M63.32,78.99c1.84,1.84,3.58,6.67,4.04,9.37"/>
			<path id="kvg:09d1f-s15" kvg:type="㇔" d="M72.2,77.29c1.34,1.42,3.46,5.68,3.8,7.76"/>
			<path id="kvg:09d1f-s16" kvg:type="㇔" d="M79.66,74.48c1.71,1.35,4.43,5.41,4.86,7.39"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09d1f" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 31.50 12.50)">1</text>
	<text transform="matrix(1 0 0 1 8.50 35.50)">2</text>
	<text transform="matrix(1 0 0 1 19.25 39.50)">3</text>
	<text transform="matrix(1 0 0 1 35.50 27.50)">4</text>
	<text transform="matrix(1 0 0 1 4.50 86.50)">5</text>
	<text transform="matrix(1 0 0 1 59.50 11.50)">6</text>
	<text transform="matrix(1 0 0 1 49.50 30.50)">7</text>
	<text transform="matrix(1 0 0 1 57.50 20.50)">8</text>
	<text transform="matrix(1 0 0 1 61.50 32.50)">9</text>
	<text transform="matrix(1 0 0 1 61.50 43.50)">10</text>
	<text transform="matrix(1 0 0 1 61.50 53.63)">11</text>
	<text transform="matrix(1 0 0 1 61.50 64.50)">12</text>
	<text transform="matrix(1 0 0 1 48.50 78.50)">13</text>
	<text transform="matrix(1 0 0 1 57.25 87.50)">14</text>
	<text transform="matrix(1 0 0 1 64.50 81.58)">15</text>
	<text transform="matrix(1 0 0 1 71.50 76.50)">16</text>
</g>
</svg>
