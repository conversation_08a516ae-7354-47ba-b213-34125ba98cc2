<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0970a" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0970a" kvg:element="霊">
	<g id="kvg:0970a-g1" kvg:element="雨" kvg:variant="true" kvg:position="top" kvg:radical="general" kvg:phon="雨+2/1">
		<path id="kvg:0970a-s1" kvg:type="㇐" d="M30.97,17.28c2.81,0.32,5.72,0.25,8.53-0.05c7.45-0.8,19.92-2.01,29.75-2.63c2.92-0.18,5.68-0.27,8.58,0.13"/>
		<path id="kvg:0970a-s2" kvg:type="㇔/㇑" d="M19.71,28.62c-0.24,5.22-2.3,11.01-3.82,16.28"/>
		<path id="kvg:0970a-s3" kvg:type="㇖b/㇆" d="M20.08,30.87c24.79-2.37,48.17-5.25,68.65-5.05c8.17,0.08,3.44,4.24-0.78,7.76"/>
		<path id="kvg:0970a-s4" kvg:type="㇑" d="M53.08,18.4c1.07,1.07,1.09,2.1,1.09,3.18c0,4-0.01,13.94-0.02,20.41c0,3.4-0.01,5.83-0.01,5.95"/>
		<path id="kvg:0970a-s5" kvg:type="㇔" d="M32.61,35.08c3.08,0.95,7.28,3.8,8.96,5.38"/>
		<path id="kvg:0970a-s6" kvg:type="㇔" d="M30.75,44.25c3.34,0.97,8.5,4.53,10.32,6.14"/>
		<path id="kvg:0970a-s7" kvg:type="㇔" d="M64.68,31.72c3.8,1.49,8.1,4.48,9.62,5.72"/>
		<path id="kvg:0970a-s8" kvg:type="㇔" d="M63.12,42.12c3.45,1,8.75,4.65,10.63,6.31"/>
	</g>
	<g id="kvg:0970a-g2" kvg:position="bottom">
		<g id="kvg:0970a-g3" kvg:element="二">
			<g id="kvg:0970a-g4" kvg:position="top" kvg:phon="雨+2/2">
				<path id="kvg:0970a-s9" kvg:type="㇐" d="M32.5,57.64c2.75,0.49,5.6,0.21,7.89-0.04c7.54-0.84,20.08-2.16,27.23-2.45c2.4-0.1,5.27-0.21,7.63,0.37"/>
			</g>
			<g id="kvg:0970a-g5" kvg:position="bottom">
				<path id="kvg:0970a-s10" kvg:type="㇐" d="M29,69.14c2.14,0.87,5.37,0.44,7.61,0.21c8.84-0.91,26.64-2.72,36.01-3.44c2.24-0.17,4.38-0.16,6.88,0.1"/>
			</g>
		</g>
		<path id="kvg:0970a-s11" kvg:type="㇑a" d="M46.27,69.83c1.08,1.08,1.26,2.29,1.26,4.27c0,4.15-0.02,13.69-0.02,18.65"/>
		<path id="kvg:0970a-s12" kvg:type="㇑a" d="M59.27,68.58c0.89,0.89,1.01,2.17,1.01,4.02c0,6.15-0.02,12.52-0.02,18.65"/>
		<path id="kvg:0970a-s13" kvg:type="㇔" d="M28.62,76.75c2.8,1.85,7.23,7.59,7.93,10.47"/>
		<path id="kvg:0970a-s14" kvg:type="㇒" d="M77.03,73.89c0.08,0.73-0.05,1.41-0.4,2.05c-2,3.18-4.51,6.19-9.32,10.03"/>
		<path id="kvg:0970a-s15" kvg:type="㇐" d="M19.25,94.2c3.3,1.18,7.11,0.66,10.49,0.35c14.7-1.36,37.51-2.55,53.76-2.91c3.13-0.07,6.42,0.24,9.5,0.9"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0970a" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 24.25 17.25)">1</text>
	<text transform="matrix(1 0 0 1 11.00 32.88)">2</text>
	<text transform="matrix(1 0 0 1 23.00 26.88)">3</text>
	<text transform="matrix(1 0 0 1 46.25 24.25)">4</text>
	<text transform="matrix(1 0 0 1 26.25 38.33)">5</text>
	<text transform="matrix(1 0 0 1 24.50 46.25)">6</text>
	<text transform="matrix(1 0 0 1 58.25 35.33)">7</text>
	<text transform="matrix(1 0 0 1 58.25 44.25)">8</text>
	<text transform="matrix(1 0 0 1 25.25 59.25)">9</text>
	<text transform="matrix(1 0 0 1 18.25 70.25)">10</text>
	<text transform="matrix(1 0 0 1 39.00 77.25)">11</text>
	<text transform="matrix(1 0 0 1 51.00 75.25)">12</text>
	<text transform="matrix(1 0 0 1 19.25 79.25)">13</text>
	<text transform="matrix(1 0 0 1 67.25 74.25)">14</text>
	<text transform="matrix(1 0 0 1 15.25 91.38)">15</text>
</g>
</svg>
