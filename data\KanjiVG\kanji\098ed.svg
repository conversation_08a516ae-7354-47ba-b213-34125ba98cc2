<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_098ed" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:098ed" kvg:element="飭">
	<g id="kvg:098ed-g1" kvg:element="⻞" kvg:variant="true" kvg:original="食" kvg:position="left" kvg:radical="general">
		<path id="kvg:098ed-s1" kvg:type="㇒" d="M32.74,11.64c0.06,0.71,0.31,1.89-0.13,2.85C29.75,20.75,21.5,33,9.81,42.22"/>
		<path id="kvg:098ed-s2" kvg:type="㇔/㇏" d="M35.52,16.83c4.34,2.01,11.21,8.26,12.29,11.38"/>
		<path id="kvg:098ed-s3" kvg:type="㇐" d="M25.75,32.16c0.36,0.15,1.02,0.19,1.39,0.15c2.3-0.25,9.87-2.17,12.1-2c0.6,0.05,0.96,0.07,1.27,0.14"/>
		<path id="kvg:098ed-s4" kvg:type="㇑" d="M20.27,40.44c0.39,0.78,0.78,1.68,0.78,2.72c0,1.04-0.13,54.29,0,55.34"/>
		<path id="kvg:098ed-s5" kvg:type="㇕" d="M21.19,42.07c2.2-0.13,18.91-2.17,20.92-2.33c1.67-0.13,2.74,1.45,2.61,2.22c-0.26,1.57-2.97,17.54-3.52,21.01"/>
		<path id="kvg:098ed-s6" kvg:type="㇐a" d="M21.58,52.23c2.97,0,18.36-2.31,21.72-2.31"/>
		<path id="kvg:098ed-s7" kvg:type="㇐a" d="M21.44,63.56C27.5,63,33.75,61.5,41.29,61.02"/>
		<path id="kvg:098ed-s8" kvg:type="㇐c" d="M21.39,76.06c3.73-0.25,15.96-2.67,19.57-2.49c0.97,0.04,1.56,0.07,2.05,0.14"/>
		<path id="kvg:098ed-s9" kvg:type="㇐c" d="M21.89,89.31c3.73-0.25,16.46-2.17,20.07-1.99c0.97,0.04,1.56,0.07,2.05,0.14"/>
	</g>
	<g id="kvg:098ed-g2" kvg:position="right">
		<g id="kvg:098ed-g3" kvg:element="𠂉">
			<path id="kvg:098ed-s10" kvg:type="㇒" d="M65.29,12.75c0.14,1.57-0.04,3.63-0.5,4.98c-2.97,8.59-6.73,15.86-13.53,26.31"/>
			<path id="kvg:098ed-s11" kvg:type="㇐" d="M62,30.89c1.02,0.11,3,0.59,4.79,0.48c8.35-0.51,19.89-3.47,25.42-3.98c1.56-0.14,3,0.18,3.79,0.37"/>
		</g>
		<g id="kvg:098ed-g4" kvg:element="力">
			<path id="kvg:098ed-s12" kvg:type="㇆" d="M54.7,51.92c0.96,0.53,2.16,0.38,3.02,0.28c8.53-0.95,27.35-3.26,31.2-3.45c3.84-0.19,4.1,1.58,3.92,4.03c-0.6,8.22-5.6,31.47-12.33,39.66c-3.94,4.81-5.6,0.24-6.6-0.53"/>
			<path id="kvg:098ed-s13" kvg:type="㇒" d="M73.84,36.68c0.07,1.35,0.18,3.5-0.15,5.44c-2.47,14.35-8.32,41.14-26.3,52.04"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_098ed" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 25.50 12.50)">1</text>
	<text transform="matrix(1 0 0 1 40.50 17.30)">2</text>
	<text transform="matrix(1 0 0 1 31.50 28.23)">3</text>
	<text transform="matrix(1 0 0 1 13.25 50.90)">4</text>
	<text transform="matrix(1 0 0 1 24.50 39.83)">5</text>
	<text transform="matrix(1 0 0 1 25.45 49.50)">6</text>
	<text transform="matrix(1 0 0 1 25.50 60.17)">7</text>
	<text transform="matrix(1 0 0 1 25.46 72.60)">8</text>
	<text transform="matrix(1 0 0 1 25.46 86.27)">9</text>
	<text transform="matrix(1 0 0 1 53.50 12.50)">10</text>
	<text transform="matrix(1 0 0 1 66.50 27.50)">11</text>
	<text transform="matrix(1 0 0 1 53.50 49.50)">12</text>
	<text transform="matrix(1 0 0 1 61.75 40.50)">13</text>
</g>
</svg>
