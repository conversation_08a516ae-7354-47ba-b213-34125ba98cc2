<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09d41" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09d41" kvg:element="鵁">
	<g id="kvg:09d41-g1" kvg:element="交" kvg:position="left">
		<g id="kvg:09d41-g2" kvg:element="亠" kvg:position="top">
			<path id="kvg:09d41-s1" kvg:type="㇑a" d="M30.74,14.5c0.68,0.48,1.79,2.33,1.79,3.29c0,3.77-0.02,7.47-0.09,10.18"/>
			<path id="kvg:09d41-s2" kvg:type="㇐" d="M11.84,31.51c0.79,0.08,2.6,0.49,3.38,0.41C24.5,31,39.83,28.19,48.68,27.2c1.3-0.15,1.98,0.04,2.95,0.52"/>
		</g>
		<g id="kvg:09d41-g3" kvg:element="父" kvg:position="bottom">
			<path id="kvg:09d41-s3" kvg:type="㇒" d="M23.48,36.01c0.26,0.74-0.01,2.47-0.63,3.68c-2.44,4.79-6.85,9.98-11.84,14.16"/>
			<path id="kvg:09d41-s4" kvg:type="㇔" d="M39.17,34.11c5.22,3.44,9.52,7.41,12.13,11.58"/>
			<path id="kvg:09d41-s5" kvg:type="㇒" d="M41.26,45.61c0.37,1.03,0.51,2.86-0.12,4.72C35.31,67.58,25.33,82.8,12.52,92.12"/>
			<path id="kvg:09d41-s6" kvg:type="㇔/㇏" d="M21.38,56.35c7.17,2.67,16.94,13.29,23.04,23.95"/>
		</g>
	</g>
	<g id="kvg:09d41-g4" kvg:element="鳥" kvg:position="right" kvg:radical="general">
		<path id="kvg:09d41-s7" kvg:type="㇒" d="M70.24,10.25c0.01,0.34,0.03,0.88-0.03,1.36c-0.34,2.83-2.28,8.9-4.93,12.43"/>
		<path id="kvg:09d41-s8" kvg:type="㇑" d="M58.99,23.27c0.33,0.43,0.6,1.02,0.6,1.73c0,7.23,0.04,35.01-0.17,46.38"/>
		<path id="kvg:09d41-s9" kvg:type="㇕a" d="M60,26.09c1.55,0,21.5-3.17,23.01-3.06c2.24,0.16,3.33,2.15,3.13,4.02C86.02,28.2,85,39.74,83.56,48.51"/>
		<path id="kvg:09d41-s10" kvg:type="㇐a" d="M60.25,36.93c1.9,0.13,22.1-2.74,24.25-2.59"/>
		<path id="kvg:09d41-s11" kvg:type="㇐a" d="M60.16,48.96c3.88-0.14,18.55-2.48,23.38-2.5"/>
		<path id="kvg:09d41-s12" kvg:type="㇐b" d="M60.38,60.21c8.18-0.84,26.79-2.86,30.3-3.38c1.22-0.19,3.3-0.35,3.92-0.11"/>
		<path id="kvg:09d41-s13" kvg:type="㇆a" d="M59.43,71.6c9.1-1.63,26.63-3.42,31.67-3.9c3.28-0.31,4.97,0.89,4.38,4.57c-1.64,10.24-4.08,19.96-6.93,26.25c-2.86,6.33-5.97,1.04-7.3-0.24"/>
		<g id="kvg:09d41-g5" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09d41-s14" kvg:type="㇔" d="M52.46,83.58C52,87.5,51.25,92.25,49.5,95.02"/>
			<path id="kvg:09d41-s15" kvg:type="㇔" d="M60.51,81.84c1.38,2.13,2.7,7.74,3.04,10.87"/>
			<path id="kvg:09d41-s16" kvg:type="㇔" d="M69.9,79.88c1.34,1.64,3.46,6.59,3.8,9.01"/>
			<path id="kvg:09d41-s17" kvg:type="㇔" d="M78.35,76.57c1.54,1.49,3.97,5.96,4.36,8.14"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09d41" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 21.50 13.50)">1</text>
	<text transform="matrix(1 0 0 1 4.50 30.50)">2</text>
	<text transform="matrix(1 0 0 1 15.50 39.50)">3</text>
	<text transform="matrix(1 0 0 1 45.50 35.40)">4</text>
	<text transform="matrix(1 0 0 1 33.50 44.50)">5</text>
	<text transform="matrix(1 0 0 1 22.50 54.50)">6</text>
	<text transform="matrix(1 0 0 1 62.50 9.50)">7</text>
	<text transform="matrix(1 0 0 1 52.50 34.00)">8</text>
	<text transform="matrix(1 0 0 1 60.00 22.50)">9</text>
	<text transform="matrix(1 0 0 1 64.00 33.50)">10</text>
	<text transform="matrix(1 0 0 1 64.50 45.50)">11</text>
	<text transform="matrix(1 0 0 1 64.50 56.50)">12</text>
	<text transform="matrix(1 0 0 1 64.50 67.30)">13</text>
	<text transform="matrix(1 0 0 1 40.50 88.50)">14</text>
	<text transform="matrix(1 0 0 1 52.75 84.65)">15</text>
	<text transform="matrix(1 0 0 1 61.00 80.82)">16</text>
	<text transform="matrix(1 0 0 1 69.75 77.50)">17</text>
</g>
</svg>
