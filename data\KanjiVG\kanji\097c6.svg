<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_097c6" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:097c6" kvg:element="韆">
	<g id="kvg:097c6-g1" kvg:element="革" kvg:position="left" kvg:radical="general">
		<g id="kvg:097c6-g2" kvg:element="廿" kvg:position="top">
			<g id="kvg:097c6-g3" kvg:element="十">
				<path id="kvg:097c6-s1" kvg:type="㇐" d="M9.4,23.38c0.62,0.27,1.77,0.31,2.4,0.27c3.27-0.24,22.84-2.71,29.72-2.83c1.04-0.02,1.67,0.13,2.19,0.26"/>
				<path id="kvg:097c6-s2" kvg:type="㇑" d="M15.35,13.46c0.47,0.33,0.75,1.48,0.84,2.14c0.81,5.9,2.23,11.44,2.73,18.9"/>
			</g>
			<path id="kvg:097c6-s3" kvg:type="㇑a" d="M34.06,10.75c0.46,0.33,0.9,1.47,0.84,2.14c-0.65,6.86-0.33,9.42-1.57,18.63"/>
			<path id="kvg:097c6-s4" kvg:type="㇐b" d="M19.33,33.25c1.77,0,14.18-0.73,15.77-0.87"/>
		</g>
		<g id="kvg:097c6-g4" kvg:position="bottom">
			<path id="kvg:097c6-s5" kvg:type="㇑" d="M10.76,42.86c0.29,0.52,0.58,0.95,0.7,1.6c0.99,5.13,1.83,8.28,2.5,17.5"/>
			<path id="kvg:097c6-s6" kvg:type="㇕" d="M11.98,44.08c8.4-1.88,22.39-2.99,26.02-3.56c1.76-0.27,3.43,1.1,3.2,2.64c-0.47,3.17-2.25,8.02-3.28,13.54"/>
			<path id="kvg:097c6-s7" kvg:type="㇐" d="M13.75,59.67c6.75-0.42,18.5-1.62,25.48-2.17"/>
			<path id="kvg:097c6-s8" kvg:type="㇐" d="M9.25,73.7c0.86,0.54,2.43,0.64,3.29,0.54C19,73.5,31.25,72,38.49,71.51c1.43-0.1,2.29,0.26,3.01,0.53"/>
			<path id="kvg:097c6-s9" kvg:type="㇑" d="M25.96,35.75c0.36,1,0.54,2.23,0.54,3.25c0,8.5,0,52.75-0.12,59"/>
		</g>
	</g>
	<g id="kvg:097c6-g5" kvg:element="遷" kvg:variant="true" kvg:position="right">
		<g id="kvg:097c6-g6" kvg:position="nyoc">
			<g id="kvg:097c6-g7" kvg:element="覀" kvg:variant="true" kvg:original="襾" kvg:position="top">
				<path id="kvg:097c6-s10" kvg:type="㇐" d="M58.61,14.91c1.06,0.31,3,0.3,4.06,0.31c7.32,0.03,23.57-1.47,30.33-2.1c1.75-0.17,2.82,0.14,3.71,0.3"/>
				<path id="kvg:097c6-s11" kvg:type="㇑" d="M56.5,24.18c0.28,0.47,0.57,0.86,0.69,1.44c0.97,4.64,1.59,7.41,2.51,11.43"/>
				<path id="kvg:097c6-s12" kvg:type="㇕" d="M57.62,24.92c8.13-0.67,34.9-2.1,38.42-2.41c1.29-0.11,2.02,1.11,1.88,1.78c-0.97,4.45-1.43,5.1-2.96,10.53"/>
				<path id="kvg:097c6-s13" kvg:type="㇑" d="M67.93,15.86c0.74,0.34,1.31,1.52,1.34,2.22c0.18,5.62,0.41,10.09,0.91,16.6"/>
				<path id="kvg:097c6-s14" kvg:type="㇑" d="M81.84,14.58c0.74,0.34,1.05,1.56,1.05,3.13c-0.03,6.26-0.67,12.65-0.67,16.41"/>
				<path id="kvg:097c6-s15" kvg:type="㇐" d="M60.07,35.73c5.7-0.8,27.83-1.89,35.05-2.31"/>
			</g>
			<g id="kvg:097c6-g8" kvg:position="bottom">
				<g id="kvg:097c6-g9" kvg:element="大">
					<path id="kvg:097c6-s16" kvg:type="㇐" d="M57.29,47.39c1.51,0.36,3.2,0.19,4.18,0.1C75.95,46.13,80,45.7,92.98,44.62c1.24-0.1,3.48-0.07,4.87-0.07"/>
					<path id="kvg:097c6-s17" kvg:type="㇒" d="M74.28,38.78c-0.17,1.39-0.05,1.77-0.38,2.44C69.5,50,64,57.5,56.17,64.75"/>
					<path id="kvg:097c6-s18" kvg:type="㇏" d="M81.26,45.93c3.25,3.6,10.83,12.78,14.07,15.2c1.1,0.82,1.49,0.94,1.99,1.02"/>
				</g>
				<g id="kvg:097c6-g10" kvg:element="己" kvg:variant="true">
					<g id="kvg:097c6-g11" kvg:element="卩" kvg:variant="true">
						<path id="kvg:097c6-s19" kvg:type="㇆" d="M65.38,60.15c0.55,0.07,2.17,0.63,3.23,0.4c4.89-1.05,12.46-2.67,13.92-2.9c2.21-0.34,3.64,0.6,3.55,1.97c-0.15,2.32-1.34,5.14-3.58,9.94c-1.45,3.1-4,0.33-4.41,0.19"/>
						<path id="kvg:097c6-s20" kvg:type="㇟/㇑" d="M67.49,60.56c0.58,0.81,0.58,1.27,0.58,2.51c0,7.45-0.27,7.78-0.27,11.58c0,7.62,1.62,7.85,13.94,7.85c14.76,0,14.9-3.14,14.9-8.71"/>
					</g>
				</g>
			</g>
		</g>
		<g id="kvg:097c6-g12" kvg:element="辶" kvg:position="nyo">
			<path id="kvg:097c6-s21" kvg:type="㇔" d="M45.58,13.5c3.06,1.13,7.92,4.63,8.68,6.38"/>
			<path id="kvg:097c6-s22" kvg:type="㇔" d="M43.12,31.5c3.34,1.26,8.64,5.18,9.47,7.13"/>
			<path id="kvg:097c6-s23" kvg:type="㇋" d="M43,56.94c1.73,0.85,2.89,0.43,3.66,0.21c0.77-0.21,6.17-2.49,7.32-2.92c1.16-0.43,2.89,0.57,2.12,1.85c-0.77,1.28-4.08,7.19-4.66,8.26c-0.58,1.07-1.39,2.99-0.23,4.48c1.16,1.49,3.62,3.2,4.2,4.48c0.58,1.28,0.58,2.35-0.58,3.42c-1.16,1.07-9.44,7.54-10.59,7.97"/>
			<path id="kvg:097c6-s24" kvg:type="㇏" d="M41.46,86.07c2.28-0.24,6.85-0.85,10.27-0.37c3.42,0.49,22.88,3.76,26.25,4.39c9.13,1.71,16.17,2.19,23.02,1.34"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_097c6" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 3.50 23.50)">1</text>
	<text transform="matrix(1 0 0 1 9.50 10.85)">2</text>
	<text transform="matrix(1 0 0 1 32.71 8.85)">3</text>
	<text transform="matrix(1 0 0 1 21.50 31.50)">4</text>
	<text transform="matrix(1 0 0 1 4.50 49.50)">5</text>
	<text transform="matrix(1 0 0 1 12.50 40.50)">6</text>
	<text transform="matrix(1 0 0 1 17.50 56.50)">7</text>
	<text transform="matrix(1 0 0 1 1.60 74.50)">8</text>
	<text transform="matrix(1 0 0 1 30.50 39.50)">9</text>
	<text transform="matrix(1 0 0 1 54.50 12.50)">10</text>
	<text transform="matrix(1 0 0 1 49.50 29.50)">11</text>
	<text transform="matrix(1 0 0 1 58.50 23.80)">12</text>
	<text transform="matrix(1 0 0 1 71.25 21.73)">13</text>
	<text transform="matrix(1 0 0 1 85.25 21.65)">14</text>
	<text transform="matrix(1 0 0 1 60.50 32.58)">15</text>
	<text transform="matrix(1 0 0 1 53.50 44.50)">16</text>
	<text transform="matrix(1 0 0 1 64.25 43.43)">17</text>
	<text transform="matrix(1 0 0 1 88.25 52.35)">18</text>
	<text transform="matrix(1 0 0 1 69.50 56.28)">19</text>
	<text transform="matrix(1 0 0 1 58.25 70.50)">20</text>
	<text transform="matrix(1 0 0 1 41.50 11.50)">21</text>
	<text transform="matrix(1 0 0 1 39.50 29.50)">22</text>
	<text transform="matrix(1 0 0 1 41.50 54.50)">23</text>
	<text transform="matrix(1 0 0 1 34.50 83.50)">24</text>
</g>
</svg>
