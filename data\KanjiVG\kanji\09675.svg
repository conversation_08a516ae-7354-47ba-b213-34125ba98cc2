<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09675" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09675" kvg:element="陵">
	<g id="kvg:09675-g1" kvg:element="⻖" kvg:variant="true" kvg:original="阜" kvg:position="left" kvg:radical="general">
		<path id="kvg:09675-s1" kvg:type="㇇" d="M16.92,19.67c1.35,0.65,3.16,0.72,4.64,0.43c4.94-0.97,8.31-1.97,12.61-2.93c3.09-0.69,4.41,1.08,3.42,3.58c-1.33,3.38-5.33,11.38-8.33,16.5"/>
		<path id="kvg:09675-s2" kvg:type="㇁" d="M29.48,37.5c12.09,7.56,7.89,32.88-1.24,25.3"/>
		<path id="kvg:09675-s3" kvg:type="㇑" d="M18.37,21c1.06,1.06,1.21,2.25,1.21,3.75c0,6.87,0.03,49.25,0.03,61c0,4.42,0,7.88,0,9.75"/>
	</g>
	<g id="kvg:09675-g2" kvg:element="夌" kvg:variant="true" kvg:position="right" kvg:phon="坴T+夂">
		<g id="kvg:09675-g3" kvg:element="土">
			<path id="kvg:09675-s4" kvg:type="㇐" d="M51.19,23.59c2.18,0.53,3.59,0.35,5.2,0.16c6.71-0.8,14.91-2.08,21.61-2.51c1.6-0.1,3.24-0.27,4.82,0.1"/>
			<path id="kvg:09675-s5" kvg:type="㇑a" d="M65.93,11.62c0.79,0.79,1.19,1.74,1.19,3.26c0,4.77-0.04,12.87-0.04,20.56"/>
			<path id="kvg:09675-s6" kvg:type="㇐" d="M43.26,37.71c3.04,0.57,5.95,0.22,8.99-0.13c10.54-1.2,24.87-3.11,34.5-3.89c2.67-0.21,5.4-0.6,8,0.17"/>
		</g>
		<g id="kvg:09675-g4" kvg:element="儿" kvg:variant="true" kvg:original="八">
			<g id="kvg:09675-g5" kvg:element="丿" kvg:position="left">
				<path id="kvg:09675-s7" kvg:type="㇒" d="M58.14,41.25c0.11,1.03,0.04,2.05-0.41,2.96c-2.1,4.17-6.01,9.87-13.94,15.71"/>
			</g>
			<g id="kvg:09675-g6" kvg:position="right">
				<path id="kvg:09675-s8" kvg:type="㇟" d="M75.35,37.12c0.7,0.7,0.95,1.63,0.95,2.81c0,3.36-0.02,6.11-0.02,8.4c0,3.54,1.47,4.73,9,4.73c4.71,0,7.81-0.69,8.47-1.08"/>
			</g>
		</g>
		<g id="kvg:09675-g7" kvg:element="夂">
			<path id="kvg:09675-s9" kvg:type="㇒" d="M62,53.5c0.1,0.97-0.25,1.97-0.69,2.78C58.75,61.01,53.3,69.1,43.7,76.65"/>
			<path id="kvg:09675-s10" kvg:type="㇇" d="M61.5,62.88c1.63,0.18,3.07,0.28,4.65-0.16c3.48-0.97,5.6-1.47,8.49-2.44c3.12-1.06,4.49,0.15,2.91,3.3c-4.4,8.83-19.7,26.24-35.47,31.68"/>
			<path id="kvg:09675-s11" kvg:type="㇏" d="M55.26,66.89c3.73,3.76,19.65,16.99,27.82,23.29c2.48,1.92,4.93,3.44,8.1,5.03"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09675" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 20.00 16.38)">1</text>
	<text transform="matrix(1 0 0 1 26.00 46.38)">2</text>
	<text transform="matrix(1 0 0 1 11.00 29.88)">3</text>
	<text transform="matrix(1 0 0 1 48.50 20.88)">4</text>
	<text transform="matrix(1 0 0 1 57.50 11.88)">5</text>
	<text transform="matrix(1 0 0 1 42.50 34.38)">6</text>
	<text transform="matrix(1 0 0 1 50.25 47.25)">7</text>
	<text transform="matrix(1 0 0 1 68.00 44.88)">8</text>
	<text transform="matrix(1 0 0 1 53.25 60.25)">9</text>
	<text transform="matrix(1 0 0 1 66.25 58.38)">10</text>
	<text transform="matrix(1 0 0 1 51.50 79.38)">11</text>
</g>
</svg>
