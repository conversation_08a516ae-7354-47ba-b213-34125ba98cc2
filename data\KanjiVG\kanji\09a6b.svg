<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09a6b" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09a6b" kvg:element="驫">
	<g id="kvg:09a6b-g1" kvg:element="馬" kvg:position="top" kvg:radical="general">
		<path id="kvg:09a6b-s1" kvg:type="㇑" d="M37.04,11.99c0.31,0.2,0.64,1.29,0.64,1.63c0,3.46,0.03,17.34-0.15,22.8"/>
		<path id="kvg:09a6b-s2" kvg:type="㇐b" d="M38.58,13.45C46.13,12.77,72,11,75.5,11.05c1.14,0.02,3.05,0.22,3.62,0.32"/>
		<path id="kvg:09a6b-s3" kvg:type="㇑a" d="M55.46,13.16c0.31,0.2,0.55,0.47,0.55,0.81c0,3.46,0.03,14.25-0.15,19.71"/>
		<path id="kvg:09a6b-s4" kvg:type="㇐b" d="M38.24,20.65c7.55-0.68,30.34-2,33.58-2.38c1.13-0.13,3.05,0.22,3.62,0.32"/>
		<path id="kvg:09a6b-s5" kvg:type="㇐b" d="M38.41,27.46c7.55-0.68,30.34-2,33.58-2.38c1.13-0.13,3.05,0.22,3.62,0.32"/>
		<path id="kvg:09a6b-s6" kvg:type="㇆a" d="M37.54,35.94c9.56-1.24,33.52-2.34,38.82-2.8c3.45-0.3,4.95,1.35,4.59,3.41c-1,5.86-1.95,8.95-4.2,12.45c-3.41,5.29-4.75,3.51-7.38-0.03"/>
		<g id="kvg:09a6b-g2" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09a6b-s7" kvg:type="㇔" d="M31.72,41.94c-0.72,3.81-1.97,7.31-4.38,9.63"/>
			<path id="kvg:09a6b-s8" kvg:type="㇔" d="M40.98,41.39c1.61,1.24,3.14,4.65,3.55,6.59"/>
			<path id="kvg:09a6b-s9" kvg:type="㇔" d="M52.26,39.69c1.11,1.11,2.87,4.58,3.15,6.32"/>
			<path id="kvg:09a6b-s10" kvg:type="㇔" d="M64.64,38.75c1.14,0.76,2.94,3.13,3.22,4.32"/>
		</g>
	</g>
	<g id="kvg:09a6b-g3" kvg:position="bottom">
		<g id="kvg:09a6b-g4" kvg:element="馬" kvg:position="left">
			<path id="kvg:09a6b-s11" kvg:type="㇑" d="M16.81,58.49c0.26,0.2,0.53,1.29,0.53,1.63c0,3.46,0.03,18.34-0.13,23.8"/>
			<path id="kvg:09a6b-s12" kvg:type="㇐b" d="M18.09,59.95c6.28-0.68,22.93-3.03,25.62-3.4c0.94-0.13,2.53-0.28,3.01-0.18"/>
			<path id="kvg:09a6b-s13" kvg:type="㇑a" d="M31.45,59.66c0.26,0.2,0.46,0.47,0.46,0.81c0,3.46,0.03,15.25-0.13,20.71"/>
			<path id="kvg:09a6b-s14" kvg:type="㇐b" d="M17.81,67.65c6.28-0.68,20.81-2.5,23.5-2.88c0.94-0.13,2.53-0.28,3.01-0.18"/>
			<path id="kvg:09a6b-s15" kvg:type="㇐b" d="M17.45,75.46c6.28-0.68,21.31-2.5,24-2.88c0.94-0.13,2.53-0.28,3.01-0.18"/>
			<path id="kvg:09a6b-s16" kvg:type="㇆a" d="M17.22,83.94c7.94-1.24,23.52-3.34,27.92-3.8c2.86-0.3,4.02,1.33,3.82,3.41c-0.46,4.7-1.96,8.95-3.32,12.45c-2.16,5.54-3.56,2.74-6.47-0.03"/>
			<g id="kvg:09a6b-g5" kvg:element="灬" kvg:variant="true" kvg:original="火">
				<path id="kvg:09a6b-s17" kvg:type="㇔" d="M12.82,88.69c0.33,3.13-1.24,6.42-2.23,8.38"/>
				<path id="kvg:09a6b-s18" kvg:type="㇔" d="M19.86,89.39c1.07,1.24,2.09,4.66,2.36,6.59"/>
				<path id="kvg:09a6b-s19" kvg:type="㇔" d="M28.16,87.19c0.85,1.03,2.2,4.22,2.42,5.82"/>
				<path id="kvg:09a6b-s20" kvg:type="㇔" d="M37.55,85.75c0.81,0.76,2.1,3.13,2.3,4.32"/>
			</g>
		</g>
		<g id="kvg:09a6b-g6" kvg:element="馬" kvg:position="right">
			<path id="kvg:09a6b-s21" kvg:type="㇑" d="M60.81,56.99c0.25,0.2,0.53,1.29,0.53,1.63c0,3.46,0.03,19.84-0.13,25.3"/>
			<path id="kvg:09a6b-s22" kvg:type="㇐b" d="M62.09,58.45c6.28-0.68,23.42-3.03,26.12-3.4c0.94-0.13,2.53-0.28,3.01-0.18"/>
			<path id="kvg:09a6b-s23" kvg:type="㇑a" d="M74.45,58.66c0.25,0.2,0.46,0.47,0.46,0.81c0,3.46,0.03,16.25-0.13,21.71"/>
			<path id="kvg:09a6b-s24" kvg:type="㇐b" d="M61.8,67.65c6.28-0.68,21.31-2.5,24-2.88c0.94-0.13,2.53-0.28,3.01-0.18"/>
			<path id="kvg:09a6b-s25" kvg:type="㇐b" d="M61.95,74.96c6.28-0.68,21.31-2,24-2.38c0.94-0.13,2.53-0.28,3.01-0.18"/>
			<path id="kvg:09a6b-s26" kvg:type="㇆a" d="M61.22,83.94c7.94-1.24,26.02-3.34,30.42-3.8c2.86-0.3,4.11,1.35,3.82,3.41c-0.83,5.86-2.12,10.29-4.32,13.95c-3.08,5.09-4.89,1.26-5.96-1.03"/>
			<g id="kvg:09a6b-g7" kvg:element="灬" kvg:variant="true" kvg:original="火">
				<path id="kvg:09a6b-s27" kvg:type="㇔" d="M56.22,89.69c-0.22,2.06-1.75,6.53-2.79,8.38"/>
				<path id="kvg:09a6b-s28" kvg:type="㇔" d="M63.86,88.64c1.07,1.19,2.09,4.48,2.36,6.34"/>
				<path id="kvg:09a6b-s29" kvg:type="㇔" d="M72.88,87.19c0.78,1.03,2.02,4.22,2.22,5.82"/>
				<path id="kvg:09a6b-s30" kvg:type="㇔" d="M82.02,86c0.83,0.72,2.14,2.95,2.35,4.07"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09a6b" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 30.25 20.88)">1</text>
	<text transform="matrix(1 0 0 1 42.00 10.05)">2</text>
	<text transform="matrix(1 0 0 1 59.00 18.50)">3</text>
	<text transform="matrix(1 0 0 1 42.50 19.40)">4</text>
	<text transform="matrix(1 0 0 1 42.25 26.83)">5</text>
	<text transform="matrix(1 0 0 1 42.00 33.50)">6</text>
	<text transform="matrix(1 0 0 1 24.50 44.18)">7</text>
	<text transform="matrix(1 0 0 1 36.50 46.50)">8</text>
	<text transform="matrix(1 0 0 1 47.00 44.50)">9</text>
	<text transform="matrix(1 0 0 1 56.75 43.45)">10</text>
	<text transform="matrix(1 0 0 1 8.50 66.50)">11</text>
	<text transform="matrix(1 0 0 1 17.75 56.55)">12</text>
	<text transform="matrix(1 0 0 1 35.50 63.50)">13</text>
	<text transform="matrix(1 0 0 1 20.00 65.40)">14</text>
	<text transform="matrix(1 0 0 1 20.16 73.58)">15</text>
	<text transform="matrix(1 0 0 1 20.04 81.00)">16</text>
	<text transform="matrix(1 0 0 1 4.50 90.50)">17</text>
	<text transform="matrix(1 0 0 1 13.50 95.50)">18</text>
	<text transform="matrix(1 0 0 1 21.50 92.50)">19</text>
	<text transform="matrix(1 0 0 1 30.25 89.50)">20</text>
	<text transform="matrix(1 0 0 1 51.50 65.50)">21</text>
	<text transform="matrix(1 0 0 1 62.50 55.50)">22</text>
	<text transform="matrix(1 0 0 1 78.00 62.50)">23</text>
	<text transform="matrix(1 0 0 1 64.34 65.50)">24</text>
	<text transform="matrix(1 0 0 1 64.50 73.13)">25</text>
	<text transform="matrix(1 0 0 1 64.38 81.50)">26</text>
	<text transform="matrix(1 0 0 1 51.25 89.50)">27</text>
	<text transform="matrix(1 0 0 1 57.50 96.38)">28</text>
	<text transform="matrix(1 0 0 1 67.25 93.50)">29</text>
	<text transform="matrix(1 0 0 1 75.25 90.50)">30</text>
</g>
</svg>
