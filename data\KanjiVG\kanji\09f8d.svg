<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09f8d" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09f8d" kvg:element="龍" kvg:radical="general">
	<g id="kvg:09f8d-g1" kvg:position="left">
		<g id="kvg:09f8d-g2" kvg:element="立" kvg:position="top">
			<g id="kvg:09f8d-g3" kvg:element="亠" kvg:position="top">
				<path id="kvg:09f8d-s1" kvg:type="㇑a" d="M30.62,12.25c0.61,0.62,1.53,2,1.53,3.58c0,2.17,0,3.42,0,6.85"/>
				<path id="kvg:09f8d-s2" kvg:type="㇐" d="M14.6,24.83c0.66,0.3,2.94,0.35,3.6,0.3c3.74-0.26,25-3.69,30.65-3.91c1.1-0.04,1.77,0.14,2.32,0.3"/>
			</g>
			<g id="kvg:09f8d-g4" kvg:position="bottom">
				<path id="kvg:09f8d-s3" kvg:type="㇔" d="M22.32,28.77c2.25,3.98,3.28,8.05,3.48,10.67"/>
				<path id="kvg:09f8d-s4" kvg:type="㇒" d="M42.57,25.18c0.53,0.45,0.59,1.67,0.53,2.04c-0.42,2.53-4.81,10.05-5.86,11.98"/>
				<path id="kvg:09f8d-s5" kvg:type="㇐" d="M10.75,42.71c0.8,0.42,1.72,0.47,2.52,0.42c9.48-0.63,28.09-3.94,38.15-4.81c1.33-0.11,2.13,0.2,2.79,0.41"/>
			</g>
		</g>
		<g id="kvg:09f8d-g5" kvg:element="月" kvg:position="bottom">
			<path id="kvg:09f8d-s6" kvg:type="㇑" d="M20.17,51.99c0.47,0.63,0.78,1.26,0.93,1.88c0.16,0.63,0.22,41.54,0.16,42.95"/>
			<path id="kvg:09f8d-s7" kvg:type="㇕" d="M22.03,52.61c1.87-0.16,19-2.81,20.39-2.98c2.49-0.31,3.42,2.04,3.11,2.98c-0.3,0.91-0.47,23.41-0.47,36.12c0,11.27-3.38,6.33-6.79,2.91"/>
			<path id="kvg:09f8d-s8" kvg:type="㇐" d="M22.03,64.19c6.38-0.94,16.81-1.85,22.41-2.32"/>
			<path id="kvg:09f8d-s9" kvg:type="㇐" d="M22.5,75.59c4.98-0.47,15.88-1.88,21.64-2.2"/>
		</g>
	</g>
	<g id="kvg:09f8d-g6" kvg:position="right">
		<path id="kvg:09f8d-s10" kvg:type="㇐" d="M66.25,23.25c4.76-0.52,14.5-2.5,19.89-3.47c1.23-0.22,1.99,0.14,2.61,0.3"/>
		<path id="kvg:09f8d-s11" kvg:type="㇞" d="M63.14,12.49c1.39,1.06,1.79,3.65,1.81,5.13c0.05,3.13-0.2,12.88-0.34,17.21c-0.03,0.93,0.28,2.08,2.81,1.64c5.58-0.97,13.71-2.21,19.85-3.49c3.48-0.72,3.58,0.81,3.03,2.42c-1.05,3.1-1.72,8.43-2.8,13.85"/>
		<path id="kvg:09f8d-s12" kvg:type="㇐" d="M64.75,51.75c4.76-0.53,14.75-2.5,21.39-2.97"/>
		<path id="kvg:09f8d-s13" kvg:type="㇟" d="M62.99,49.37c0.74,1.19,1.33,2.59,1.38,4.43c0.2,8.19-0.53,24.64-0.53,30.2c0,13,8.9,11.53,17.67,11.53c14,0,14.89-2.78,14.89-9.12"/>
		<path id="kvg:09f8d-s14" kvg:type="㇐" d="M65,61.54c4.76-0.53,15.5-2,20.89-2.97c1.23-0.22,1.99,0.14,2.61,0.3"/>
		<path id="kvg:09f8d-s15" kvg:type="㇐" d="M64.75,71c4.76-0.53,17-1.5,22.39-2.47c1.23-0.22,1.99,0.14,2.61,0.3"/>
		<path id="kvg:09f8d-s16" kvg:type="㇐" d="M64.25,81c4.76-0.53,18.5-2,23.89-2.97c1.23-0.22,1.99,0.14,2.61,0.3"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09f8d" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 23.25 12.50)">1</text>
	<text transform="matrix(1 0 0 1 8.25 24.50)">2</text>
	<text transform="matrix(1 0 0 1 16.50 34.48)">3</text>
	<text transform="matrix(1 0 0 1 35.50 31.63)">4</text>
	<text transform="matrix(1 0 0 1 3.50 43.50)">5</text>
	<text transform="matrix(1 0 0 1 14.25 60.13)">6</text>
	<text transform="matrix(1 0 0 1 24.75 50.50)">7</text>
	<text transform="matrix(1 0 0 1 26.25 60.50)">8</text>
	<text transform="matrix(1 0 0 1 26.24 71.50)">9</text>
	<text transform="matrix(1 0 0 1 71.25 18.13)">10</text>
	<text transform="matrix(1 0 0 1 56.25 9.13)">11</text>
	<text transform="matrix(1 0 0 1 65.50 48.50)">12</text>
	<text transform="matrix(1 0 0 1 53.25 57.50)">13</text>
	<text transform="matrix(1 0 0 1 68.50 58.78)">14</text>
	<text transform="matrix(1 0 0 1 68.50 68.50)">15</text>
	<text transform="matrix(1 0 0 1 68.50 77.50)">16</text>
</g>
</svg>
