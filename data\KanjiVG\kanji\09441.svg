<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09441" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09441" kvg:element="鑁">
	<g id="kvg:09441-g1" kvg:element="金" kvg:position="left" kvg:radical="general">
		<path id="kvg:09441-s1" kvg:type="㇒" d="M28.75,14.74c0,0.81,0.05,1.46-0.15,2.43c-1.07,5.06-11.2,21.96-19.87,29.45"/>
		<path id="kvg:09441-s2" kvg:type="㇔/㇏" d="M30.39,19.46c3.86,3.04,9.68,9.42,11.86,14.54"/>
		<path id="kvg:09441-s3" kvg:type="㇐" d="M18,40.86c1.6,0,2.32,0.1,2.84,0.06c4.61-0.35,9.94-1.72,15.37-1.97c0.79-0.04,0.95-0.06,2.29-0.06"/>
		<path id="kvg:09441-s4" kvg:type="㇐" d="M12.09,54.81c0.59,0.33,2.56,0.48,3.17,0.43c5.49-0.49,15.24-2.49,22.28-2.67c0.75-0.02,2.38-0.13,3.49,0.15"/>
		<path id="kvg:09441-s5" kvg:type="㇑a" d="M25.95,41.69c1.24,0.78,1.24,2.52,1.24,3.14c0,4.35-0.19,34.92-0.19,39.48"/>
		<path id="kvg:09441-s6" kvg:type="㇔" d="M13.14,64.51c3.34,5.32,4.97,11.08,5.65,14.19"/>
		<path id="kvg:09441-s7" kvg:type="㇒" d="M38.7,59.05c0.3,0.84,0.46,2.06,0.34,2.76c-0.3,1.81-3.21,6.13-6.62,11.47"/>
		<path id="kvg:09441-s8" kvg:type="㇀/㇐" d="M11.75,90.72c1.22,1.04,2.68,0.78,4.38,0.26C17.39,90.6,27.09,85,39.75,78.75"/>
	</g>
	<g id="kvg:09441-g2" kvg:element="𡕰" kvg:position="right">
		<g id="kvg:09441-g3" kvg:position="top">
			<path id="kvg:09441-s9" kvg:type="㇒" d="M79.24,12.64c0.06,0.61,0.13,1.59-0.12,2.47c-1.48,5.2-9.96,16.62-21.56,23.61"/>
			<path id="kvg:09441-s10" kvg:type="㇏" d="M59.52,19.83c7.69,3.42,19.87,14.06,21.79,19.38"/>
			<path id="kvg:09441-s11" kvg:type="㇏" d="M63.77,13.33c2.22,0.95,5.74,3.91,6.29,5.38"/>
			<path id="kvg:09441-s12" kvg:type="㇔" d="M55.77,25.83c1.96,0.86,5.05,3.54,5.54,4.88"/>
			<path id="kvg:09441-s13" kvg:type="㇔" d="M79.02,25.33c2.04,0.91,5.28,3.72,5.79,5.13"/>
			<path id="kvg:09441-s14" kvg:type="㇔" d="M68.27,35.08c1.78,0.95,4.6,3.91,5.04,5.38"/>
			<path id="kvg:09441-s15" kvg:type="㇄" d="M48.81,15.94c0.44,0.28,1.38,2.13,1.47,2.7c0.65,4.15-0.49,24.42-0.54,25.76c-0.04,1.34,0.96,1.26,1.84,1.2c8.04-0.52,32.89-2.65,37.48-2.94"/>
			<path id="kvg:09441-s16" kvg:type="㇑" d="M90.66,12.88c0.46,0.29,1.2,2.15,1.15,2.73c-0.24,2.66-1.4,17.53-2.7,30.27"/>
			<path id="kvg:09441-s17" kvg:type="㇒" d="M61.01,47.5c0.07,0.38,0.08,0.97-0.14,1.52c-1.92,4.84-4.39,8.54-12.62,13.23"/>
			<path id="kvg:09441-s18" kvg:type="㇄" d="M75.75,44.5c0.91,0.81,0.68,1.78,0.68,3.03c0,1.69-0.27,3.86-0.27,5.07c0,4.65,0.84,4.65,9.03,4.65c5.36,0,9.31,0,11.58-1.06"/>
		</g>
		<g id="kvg:09441-g4" kvg:element="夂" kvg:position="bottom">
			<path id="kvg:09441-s19" kvg:type="㇒" d="M63.48,57c0.06,0.62,0.12,1.61-0.11,2.5c-1.36,5.28-9.16,16.85-19.83,23.94"/>
			<path id="kvg:09441-s20" kvg:type="㇇" d="M63.73,66.11c0.3,0.03,1.94,0.27,2.8,0.19c3.87-0.35,9.76-1.12,13.82-2c2.79-0.6,3.58,0.68,2.87,2.32c-3.9,8.93-25.07,28.27-42.46,32.87"/>
			<path id="kvg:09441-s21" kvg:type="㇏" d="M58.54,70.42c4.65,4.51,25.74,21.66,32.01,25.78c1.87,1.23,3.38,1.83,5.2,2.19"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09441" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 20.85 14.50)">1</text>
	<text transform="matrix(1 0 0 1 33.50 19.50)">2</text>
	<text transform="matrix(1 0 0 1 23.50 37.50)">3</text>
	<text transform="matrix(1 0 0 1 5.50 55.50)">4</text>
	<text transform="matrix(1 0 0 1 19.50 50.50)">5</text>
	<text transform="matrix(1 0 0 1 6.58 65.50)">6</text>
	<text transform="matrix(1 0 0 1 31.50 61.50)">7</text>
	<text transform="matrix(1 0 0 1 4.50 92.50)">8</text>
	<text transform="matrix(1 0 0 1 72.50 11.50)">9</text>
	<text transform="matrix(1 0 0 1 52.75 17.50)">10</text>
	<text transform="matrix(1 0 0 1 60.50 11.50)">11</text>
	<text transform="matrix(1 0 0 1 52.50 24.50)">12</text>
	<text transform="matrix(1 0 0 1 79.50 23.23)">13</text>
	<text transform="matrix(1 0 0 1 62.50 40.50)">14</text>
	<text transform="matrix(1 0 0 1 39.50 14.50)">15</text>
	<text transform="matrix(1 0 0 1 85.50 11.50)">16</text>
	<text transform="matrix(1 0 0 1 50.50 54.50)">17</text>
	<text transform="matrix(1 0 0 1 78.50 51.50)">18</text>
	<text transform="matrix(1 0 0 1 53.25 64.50)">19</text>
	<text transform="matrix(1 0 0 1 67.50 62.50)">20</text>
	<text transform="matrix(1 0 0 1 55.50 81.50)">21</text>
</g>
</svg>
