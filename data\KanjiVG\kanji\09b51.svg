<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09b51" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09b51" kvg:element="魑">
	<g id="kvg:09b51-g1" kvg:element="鬼" kvg:position="nyo" kvg:radical="general">
		<g id="kvg:09b51-g2" kvg:element="丿">
			<path id="kvg:09b51-s1" kvg:type="㇒" d="M 31.11,12 c 0.03,0.34 0.1,0.97 -0.05,1.35 -1.88,4.74 -4.66,9.08 -9.56,12.9"/>
		</g>
		<g id="kvg:09b51-g3" kvg:element="田">
			<path id="kvg:09b51-s2" kvg:type="㇑" d="M 14.33,28.12 c 0.23,0.54 0.24,0.91 0.37,1.57 1.04,5.5 2.33,15.74 2.95,25.88"/>
			<path id="kvg:09b51-s3" kvg:type="㇕a" d="M 15.43,29.45 C 23.73,28.89 38,25.96 44.2,25.42 46.48,25.22 47.41,27 47.16,29.29 46.59,34.67 45.5,46 44.14,53.63"/>
			<path id="kvg:09b51-s4" kvg:type="㇑a" d="M 29.2,28.9 c 0.69,0.69 1.25,1.64 1.26,2.75 0.03,5.51 0.12,16.88 0.12,20"/>
			<path id="kvg:09b51-s5" kvg:type="㇐a" d="M 16.91,41.07 C 19.61,40.8 36.25,38.5 45.64,37.7"/>
			<path id="kvg:09b51-s6" kvg:type="㇐a" d="M 18.07,54.14 C 28.25,52.75 34,52.25 43.71,51.26"/>
		</g>
		<g id="kvg:09b51-g4" kvg:element="儿" kvg:variant="true" kvg:original="八">
			<g id="kvg:09b51-g5" kvg:element="丿" kvg:position="left">
				<path id="kvg:09b51-s7" kvg:type="㇒" d="M 23.62,58.75 c 0.17,0.67 0.47,1.96 0.25,3.05 C 22,71 19,83.25 11,92.5"/>
			</g>
			<g id="kvg:09b51-g6" kvg:position="right">
				<path id="kvg:09b51-s8" kvg:type="㇟" d="M 33.49,54.87 c 0.74,1.19 1.33,2.59 1.38,4.43 0.2,8.19 -0.42,18.64 -0.42,24.2 0,11 1.56,13.03 31.97,13.03 27.34,0 28.84,-1.53 28.84,-10.62"/>
			</g>
		</g>
		<g id="kvg:09b51-g7" kvg:element="厶">
			<path id="kvg:09b51-s9" kvg:type="㇜" d="M 45.44,60.72 c 0.21,0.35 0.06,1.93 -0.07,2.38 -1.55,5.71 -3.12,10.39 -6.06,16.54 -0.67,1.41 -0.87,1.7 0.42,1.27 4.53,-1.51 6.66,-2.82 11.05,-4.41"/>
			<path id="kvg:09b51-s10" kvg:type="㇔" d="M 48.85,71.54 c 1.01,1.37 3.24,6.8 3.49,8.93"/>
		</g>
	</g>
	<g id="kvg:09b51-g8" kvg:element="离" kvg:position="nyoc">
		<g id="kvg:09b51-g9" kvg:element="亠">
			<path id="kvg:09b51-s11" kvg:type="㇑a" d="M 71.56,11.5 c 0.56,0.27 1.48,1.33 1.48,1.87 0,2 -0.18,6.23 -0.07,7.92"/>
			<path id="kvg:09b51-s12" kvg:type="㇐" d="M 55.17,23.21 c 1.22,0.22 2.31,0.46 3.71,0.3 8.38,-0.91 21.78,-2.99 31.55,-3.43 1.44,-0.06 2.17,0.03 3.24,0.39"/>
		</g>
		<g id="kvg:09b51-g10" kvg:element="凶">
			<g id="kvg:09b51-g11" kvg:element="乂">
				<g id="kvg:09b51-g12" kvg:element="丿">
					<path id="kvg:09b51-s13" kvg:type="㇒" d="M 79.27,25.86 c 0.24,0.36 0.49,0.65 0.12,1.29 -3.27,5.72 -8.13,10.5 -15.16,13.5"/>
				</g>
				<path id="kvg:09b51-s14" kvg:type="㇔/㇏" d="M 65.53,27.3 c 5.12,1.88 12.5,7.99 15.17,12.47"/>
			</g>
			<g id="kvg:09b51-g13" kvg:element="凵" kvg:position="kamae">
				<path id="kvg:09b51-s15" kvg:type="㇄a" d="M 58.65,28.83 c 0.32,0.17 0.98,1.24 1.05,1.57 0.46,2.41 -0.41,14.38 -0.44,15.16 -0.03,0.78 0.69,1.21 1.31,1.12 9.27,-1.4 23.28,-2.66 26.56,-2.83"/>
				<path id="kvg:09b51-s16" kvg:type="㇑" d="M 87.91,25.63 c 0.32,0.17 0.98,1.24 1.05,1.57 0.06,0.33 -0.62,12.02 -0.9,18.52"/>
			</g>
		</g>
		<g id="kvg:09b51-g14" kvg:element="禸">
			<g id="kvg:09b51-g15" kvg:element="冂">
				<path id="kvg:09b51-s17" kvg:type="㇑" d="M 56.15,54.52 c 0.57,0.76 0.76,1.96 0.76,3.35 0,7.88 -0.17,26.35 -0.17,28.63"/>
				<path id="kvg:09b51-s18" kvg:type="㇆" d="M 57.08,56.98 c 5.07,-0.64 29.81,-4.09 30.83,-4.09 1.47,0 2.4,0.48 2.4,1.91 0,3.02 0.19,19.96 0.2,25.31 0.01,7.44 -4.64,3.14 -6,1.63"/>
			</g>
			<path id="kvg:09b51-s19" kvg:type="㇒" d="M 72.72,47.79 c 0.22,0.42 0.28,2.07 0.07,2.59 -2.083107,7.5 -3.347874,11.513832 -5.03813,16.954299"/>
			<path id="kvg:09b51-s20" kvg:type="㇐" d="M 64.803107,68.29397 c 3.39,-1.12 9.84,-2.13 14.56,-4.01"/>
			<path id="kvg:09b51-s21" kvg:type="㇔" d="M 77,59.08 c 2.14,2.11 5.53,8.66 6.06,11.94"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09b51" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 23.50 11.38)">1</text>
	<text transform="matrix(1 0 0 1 8.00 35.50)">2</text>
	<text transform="matrix(1 0 0 1 17.50 26.48)">3</text>
	<text transform="matrix(1 0 0 1 33.00 34.65)">4</text>
	<text transform="matrix(1 0 0 1 19.75 37.50)">5</text>
	<text transform="matrix(1 0 0 1 20.50 50.50)">6</text>
	<text transform="matrix(1 0 0 1 16.50 64.50)">7</text>
	<text transform="matrix(1 0 0 1 27.75 68.50)">8</text>
	<text transform="matrix(1 0 0 1 38.50 61.50)">9</text>
	<text transform="matrix(1 0 0 1 47.50 68.50)">10</text>
	<text transform="matrix(1 0 0 1 60.50 10.50)">11</text>
	<text transform="matrix(1 0 0 1 52.50 20.50)">12</text>
	<text transform="matrix(1 0 0 1 70.50 28.50)">13</text>
	<text transform="matrix(1 0 0 1 62.50 34.90)">14</text>
	<text transform="matrix(1 0 0 1 49.50 34.50)">15</text>
	<text transform="matrix(1 0 0 1 91.50 29.50)">16</text>
	<text transform="matrix(1 0 0 1 46.50 57.50)">17</text>
	<text transform="matrix(1 0 0 1 59.50 54.50)">18</text>
	<text transform="matrix(1 0 0 1 74.75 52.03)">19</text>
	<text transform="matrix(1 0 0 1 61.12 75.83)">20</text>
	<text transform="matrix(1 0 0 1 80.50 61.50)">21</text>
</g>
</svg>
