<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09698" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09698" kvg:element="隘">
	<g id="kvg:09698-g1" kvg:element="⻖" kvg:variant="true" kvg:original="阜" kvg:position="left" kvg:radical="general">
		<path id="kvg:09698-s1" kvg:type="㇇" d="M14.87,20.66c0.73,0.13,2.55,0.32,3.39,0.16c5.74-1.07,10.28-2.13,12.88-3.11c3.06-1.15,6.12,1.15,4.76,3.83c-1.36,2.68-6.63,13.71-9.01,17.55"/>
		<path id="kvg:09698-s2" kvg:type="㇁" d="M26.38,40.09C38.75,51,36.5,76.75,26.08,66.29"/>
		<path id="kvg:09698-s3" kvg:type="㇑" d="M16.54,22.03c0.21,1.47,0.28,2.59,0.5,3.74c0.22,1.15,0,62.8-0.22,69.99"/>
	</g>
	<g id="kvg:09698-g2" kvg:position="right">
		<g id="kvg:09698-g3" kvg:element="益" kvg:variant="true" kvg:position="top">
			<g id="kvg:09698-g4" kvg:element="八">
				<g id="kvg:09698-g5" kvg:position="left">
					<path id="kvg:09698-s4" kvg:type="㇒" d="M54.14,18.39c0.04,0.42,0.08,1.08-0.08,1.68C53.09,23.6,46.91,31.36,39.3,36.1"/>
				</g>
				<g id="kvg:09698-g6" kvg:position="right">
					<path id="kvg:09698-s5" kvg:type="㇏" d="M67.13,15.31c2.05,0.37,14.56,14.57,20.63,17.74c2.35,1.23,3.19,1.88,5.46,2.28"/>
				</g>
			</g>
			<path id="kvg:09698-s6" kvg:type="㇐" d="M51.52,34.62c1.03,0.51,2.92,0.56,3.96,0.51c4.82-0.25,14.26-1.38,19.16-1.97c1.71-0.21,2.76-0.39,3.62-0.13"/>
			<g id="kvg:09698-g7" kvg:element="八">
				<g id="kvg:09698-g8" kvg:position="left">
					<path id="kvg:09698-s7" kvg:type="㇒" d="M54.05,45.69c0.26,0.58-0.04,1.24-0.36,1.96c-2.12,4.7-10.1,15.05-14.95,17.45"/>
				</g>
				<g id="kvg:09698-g9" kvg:position="right">
					<path id="kvg:09698-s8" kvg:type="㇏" d="M65.38,42.31c2.23,0.4,15.82,15.84,22.41,19.29c2.55,1.33,3.46,2.05,5.93,2.48"/>
				</g>
			</g>
			<g id="kvg:09698-g10" kvg:element="皿" kvg:position="bottom">
				<path id="kvg:09698-s9" kvg:type="㇑a" d="M42.09,67.08c0.58,0.64,1.92,2.21,1.98,2.76c0.68,5.91,1.29,14.68,1.96,22.4"/>
				<path id="kvg:09698-s10" kvg:type="㇕b" d="M44.96,68.43c10.46-0.89,35.57-2.31,39.35-2.58c1.38-0.1,2.35,1.06,2.01,2.62c-1.07,4.96-2.51,15.94-3.5,21.57"/>
				<path id="kvg:09698-s11" kvg:type="㇑a" d="M56.52,69.04c0.24,0.41,0.6,1.69,0.6,2.21c0,6.37,0.83,11.83,0.83,20.15"/>
				<path id="kvg:09698-s12" kvg:type="㇑a" d="M70.23,68.3c0.6,1.22,0.93,1.95,0.87,2.98c-0.34,5.7-0.01,11.8-0.88,19.51"/>
				<path id="kvg:09698-s13" kvg:type="㇐" d="M32.89,92.24c1.51,0.29,4.29,0.34,5.81,0.29c15.76-0.49,33.08-1.8,52.28-1.67c2.53,0.02,4.04,0.14,5.3,0.28"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09698" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 19.50 17.50)">1</text>
	<text transform="matrix(1 0 0 1 23.50 49.50)">2</text>
	<text transform="matrix(1 0 0 1 8.75 27.50)">3</text>
	<text transform="matrix(1 0 0 1 45.50 17.50)">4</text>
	<text transform="matrix(1 0 0 1 62.50 13.50)">5</text>
	<text transform="matrix(1 0 0 1 53.50 32.50)">6</text>
	<text transform="matrix(1 0 0 1 46.50 45.50)">7</text>
	<text transform="matrix(1 0 0 1 59.50 44.50)">8</text>
	<text transform="matrix(1 0 0 1 36.50 74.88)">9</text>
	<text transform="matrix(1 0 0 1 46.50 65.50)">10</text>
	<text transform="matrix(1 0 0 1 47.50 77.50)">11</text>
	<text transform="matrix(1 0 0 1 61.00 76.50)">12</text>
	<text transform="matrix(1 0 0 1 30.50 88.50)">13</text>
</g>
</svg>
