<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_096c0" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:096c0" kvg:element="雀">
	<g id="kvg:096c0-g1" kvg:position="top">
		<g id="kvg:096c0-g2" kvg:element="少">
			<g id="kvg:096c0-g3" kvg:element="小">
				<path id="kvg:096c0-s1" kvg:type="㇚" d="M52.96,10c1.08,1.06,1.53,2.3,1.34,3.75c0,6.13-0.05,19.42-0.05,21.13c0,6.38-5,1.38-6.75-0.36"/>
				<path id="kvg:096c0-s2" kvg:type="㇒" d="M34.17,22.72c0.03,1.24-0.27,2.36-0.9,3.36c-2.52,4.4-8.66,10.93-13.61,14.36"/>
				<path id="kvg:096c0-s3" kvg:type="㇔" d="M73.5,17.95c6.37,2.69,13.88,8.43,16.5,13.81"/>
			</g>
		</g>
		<g id="kvg:096c0-g4" kvg:element="隹" kvg:part="1" kvg:radical="tradit">
			<g id="kvg:096c0-g5" kvg:element="亻" kvg:part="1" kvg:variant="true" kvg:original="人">
				<g id="kvg:096c0-g6" kvg:element="丿" kvg:radical="nelson">
					<path id="kvg:096c0-s4" kvg:type="㇒" d="M69.09,24.83c0.15,1.01-0.29,2.79-1.31,4.06C58.25,40.75,39,57.5,14.25,67.5"/>
				</g>
			</g>
		</g>
	</g>
	<g id="kvg:096c0-g7" kvg:element="隹" kvg:part="2" kvg:position="bottom" kvg:radical="tradit">
		<g id="kvg:096c0-g8" kvg:element="亻" kvg:part="2" kvg:variant="true" kvg:original="人">
			<path id="kvg:096c0-s5" kvg:type="㇑" d="M36.96,57.05c0.87,0.87,1.22,2.08,1.22,3.47c0,4.79,0,19.48,0,29.98c0,4.25,0,7.81,0,9.75"/>
		</g>
		<path id="kvg:096c0-s6" kvg:type="㇒" d="M64.82,43.5c0.05,0.52-0.02,1.21-0.19,1.66c-1.1,2.86-1.74,4.11-4.26,7.59"/>
		<path id="kvg:096c0-s7" kvg:type="㇐" d="M37.96,56.24c6.93-0.61,27.78-2.36,39.44-3.39c3.97-0.35,6.9-0.62,7.91-0.73"/>
		<path id="kvg:096c0-s8" kvg:type="㇑" d="M60.75,56.36c0.84,0.84,1.06,2.06,1.06,3.26c0,8,0.03,23.11,0.03,31.06"/>
		<path id="kvg:096c0-s9" kvg:type="㇐" d="M39.25,67.88c6.69-0.51,25.21-1.78,35.25-2.51c3.1-0.23,5.4-0.4,6.28-0.49"/>
		<path id="kvg:096c0-s10" kvg:type="㇐" d="M39.14,79.76c6.92-0.42,25.29-1.74,35.75-2.5c3.52-0.26,6.14-0.45,7.14-0.53"/>
		<path id="kvg:096c0-s11" kvg:type="㇐" d="M39.39,92.81c7.87-0.42,30.32-1.68,42.73-2.48c2.71-0.17,5.43-0.37,8.14-0.59"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_096c0" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 45.50 10.50)">1</text>
	<text transform="matrix(1 0 0 1 24.50 20.50)">2</text>
	<text transform="matrix(1 0 0 1 66.50 15.50)">3</text>
	<text transform="matrix(1 0 0 1 61.50 23.50)">4</text>
	<text transform="matrix(1 0 0 1 30.50 68.50)">5</text>
	<text transform="matrix(1 0 0 1 65.50 41.50)">6</text>
	<text transform="matrix(1 0 0 1 52.25 52.18)">7</text>
	<text transform="matrix(1 0 0 1 65.25 62.50)">8</text>
	<text transform="matrix(1 0 0 1 42.50 65.50)">9</text>
	<text transform="matrix(1 0 0 1 41.50 76.50)">10</text>
	<text transform="matrix(1 0 0 1 41.50 89.50)">11</text>
</g>
</svg>
