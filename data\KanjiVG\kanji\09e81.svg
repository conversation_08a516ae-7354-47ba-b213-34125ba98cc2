<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09e81" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09e81" kvg:element="麁">
	<g id="kvg:09e81-g1" kvg:element="𠂊" kvg:variant="true" kvg:original="勹" kvg:position="top">
		<path id="kvg:09e81-s1" kvg:type="㇒" d="M45.99,9c0.08,0.55,0.33,1.51-0.15,2.2C42,16.75,35.25,25.25,19.5,33.25"/>
		<path id="kvg:09e81-s2" kvg:type="㇇" d="M43.5,18.56c1.32,0.08,2.95,0.29,4.65,0.01c9.6-1.58,16.63-3.21,19.53-3.34c3.17-0.15,2.64,2.36,1.48,3.33C66.25,21,63.25,22.75,59.5,25"/>
	</g>
	<g id="kvg:09e81-g2" kvg:element="鹿" kvg:position="bottom" kvg:radical="general">
		<g id="kvg:09e81-g3" kvg:element="广">
			<path id="kvg:09e81-s3" kvg:type="㇑a" d="M51.77,30.63c0.69,0.3,1.8,2.27,1.8,2.89c0,1.48-0.07,4.23-0.07,6.2"/>
			<g id="kvg:09e81-g4" kvg:element="厂">
				<path id="kvg:09e81-s4" kvg:type="㇐" d="M22.2,41.68c1.74,0.43,4.95,0.59,6.69,0.43c17.36-1.61,36.09-3.15,52.13-3.98c2.9-0.15,4.65,0.2,6.11,0.42"/>
				<path id="kvg:09e81-s5" kvg:type="㇒" d="M26.2,42.69c0.04,1.26,0.09,3.24-0.08,5.05c-0.98,10.65-3.2,36.47-13.81,47.94"/>
			</g>
		</g>
		<path id="kvg:09e81-s6" kvg:type="㇕" d="M31.69,54.52c1.83,0.5,3.38,0.83,5.28,0.63c13.09-1.38,37.77-3.9,41.45-4.11c2.75-0.16,3.73,1.34,3.23,3.12c-0.92,3.28-2.14,6.53-3.38,10.06"/>
		<path id="kvg:09e81-s7" kvg:type="㇑" d="M42.76,46.48c0.8,0.33,1.29,2.04,1.45,2.7c0.16,0.66,0.62,12.7,1.07,17.25"/>
		<path id="kvg:09e81-s8" kvg:type="㇑" d="M62.69,44.56c0.8,0.33,1.49,2.03,1.45,2.7c-0.2,2.94-1.57,12.8-2.26,17.35"/>
		<path id="kvg:09e81-s9" kvg:type="㇐" d="M29.42,67.64c1.32,0.46,3.75,0.59,5.08,0.46c8.44-0.81,32.05-4.35,41.52-4.18c2.21,0.04,3.53,0.22,4.64,0.46"/>
		<g id="kvg:09e81-g5" kvg:element="比">
			<path id="kvg:09e81-s10" kvg:type="㇐" d="M35.42,81.98c4.55-0.23,9.79-1.61,13.75-2c1.16-0.12,1.9-0.11,2.5-0.04"/>
			<path id="kvg:09e81-s11" kvg:type="㇙" d="M33.93,73.5c0.82,0.42,1.52,2.15,1.52,2.72c0,1.54-0.26,17.21-0.26,19.17c0,2.18,1.66,2.35,3.29,1.66c4.27-1.79,7.26-3.23,12.38-6.06"/>
			<g id="kvg:09e81-g6" kvg:element="匕">
				<path id="kvg:09e81-s12" kvg:type="㇒" d="M81.38,73.16c-0.13,1.09-0.12,1.94-0.82,2.39C76.75,78,70.53,80.97,64.12,83.3"/>
				<path id="kvg:09e81-s13" kvg:type="㇟" d="M60.03,70.67c0.93,0.45,1.29,1.39,1.29,2.51c0,1.48,0.43,15.84,0.4,17.58C61.6,97.08,63,98.8,76.04,98.8c13.71,0,14.74-1.8,14.74-10.11"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09e81" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 38.50 9.50)">1</text>
	<text transform="matrix(1 0 0 1 51.50 14.50)">2</text>
	<text transform="matrix(1 0 0 1 43.50 32.50)">3</text>
	<text transform="matrix(1 0 0 1 25.50 39.50)">4</text>
	<text transform="matrix(1 0 0 1 17.75 50.50)">5</text>
	<text transform="matrix(1 0 0 1 29.50 51.50)">6</text>
	<text transform="matrix(1 0 0 1 36.50 49.50)">7</text>
	<text transform="matrix(1 0 0 1 55.50 47.50)">8</text>
	<text transform="matrix(1 0 0 1 29.50 64.50)">9</text>
	<text transform="matrix(1 0 0 1 37.50 78.50)">10</text>
	<text transform="matrix(1 0 0 1 25.50 80.50)">11</text>
	<text transform="matrix(1 0 0 1 70.75 73.50)">12</text>
	<text transform="matrix(1 0 0 1 48.50 74.50)">13</text>
</g>
</svg>
