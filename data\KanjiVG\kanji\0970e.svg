<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0970e" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0970e" kvg:element="霎">
	<g id="kvg:0970e-g1" kvg:element="雨" kvg:variant="true" kvg:position="top" kvg:radical="general">
		<path id="kvg:0970e-s1" kvg:type="㇐" d="M31.16,12.06c1.84,0.44,4.24,0.78,5.69,0.65C44.6,12,64.6,10.24,71.58,10.14c2.04-0.03,2.61-0.14,4.48,0.1"/>
		<path id="kvg:0970e-s2" kvg:type="㇔/㇑" d="M19.18,20.67C18.85,25.83,16,31.55,13.9,36.77"/>
		<path id="kvg:0970e-s3" kvg:type="㇖b/㇆" d="M19.85,23.32c7.4-1.02,51.84-4.84,67.55-4.84c9.1,0,1.17,7.33-0.28,8.93"/>
		<path id="kvg:0970e-s4" kvg:type="㇑" d="M53.42,12.32c0.91,1.36,1.31,2.26,1.33,3.6c0.01,0.48-0.27,17.27-0.27,18.59"/>
		<path id="kvg:0970e-s5" kvg:type="㇔" d="M33.25,24.68c3.42,0.72,8.09,2.88,9.96,4.07"/>
		<path id="kvg:0970e-s6" kvg:type="㇔" d="M28.25,31.51c3.65,0.96,9.28,4.48,11.27,6.07"/>
		<path id="kvg:0970e-s7" kvg:type="㇔" d="M66.25,23.75c4.55,1.22,9.71,3.67,11.53,4.69"/>
		<path id="kvg:0970e-s8" kvg:type="㇔" d="M65.29,31.23c3.85,1.23,9.1,4.91,11.2,6.96"/>
	</g>
	<g id="kvg:0970e-g2" kvg:element="妾" kvg:position="bottom">
		<g id="kvg:0970e-g3" kvg:element="立" kvg:position="top">
			<g id="kvg:0970e-g4" kvg:element="亠" kvg:position="top">
				<path id="kvg:0970e-s9" kvg:type="㇑a" d="M52.29,37.75c1.22,0.74,1.83,0.74,1.83,2.45s0,2.56,0,5.21"/>
				<path id="kvg:0970e-s10" kvg:type="㇐" d="M22.74,47.38c1.65,0.37,3.57,0.43,5.21,0.37c9.29-0.32,41.09-3.86,55.13-4.13c2.74-0.05,4.39,0.18,5.76,0.36"/>
			</g>
			<g id="kvg:0970e-g5" kvg:position="bottom">
				<path id="kvg:0970e-s11" kvg:type="㇔" d="M35.64,49.6c3.86,2.85,5.61,5.76,5.96,7.64"/>
				<path id="kvg:0970e-s12" kvg:type="㇒" d="M71.33,46.21c0.17,0.79,0.49,1.54,0.44,1.84c-0.35,2.05-3.59,7.56-4.47,9.12"/>
				<path id="kvg:0970e-s13" kvg:type="㇐" d="M14.75,61.28c2.3,0.5,5.01,0.63,7.29,0.5C39,60.75,70.25,58.5,86.94,57.79c3.84-0.16,6.14,0.24,8.06,0.49"/>
			</g>
		</g>
		<g id="kvg:0970e-g6" kvg:element="女" kvg:position="bottom">
			<path id="kvg:0970e-s14" kvg:type="㇛" d="M51.16,62.45c0.49,0.86,0.33,1.98-0.25,3C48,70.58,43.68,77.94,36.98,83.6c-0.98,0.83-0.26,1.5,0.75,1.49C50,84.98,72,91.5,81.25,99.5"/>
			<path id="kvg:0970e-s15" kvg:type="㇒" d="M68.87,70.21c0.14,0.72,0.24,2.49-0.28,3.54c-4.13,8.5-16.05,20.17-45.59,26.5"/>
			<path id="kvg:0970e-s16" kvg:type="㇐" d="M15.88,73.54c1.21,0.37,3.42,0.4,5.85,0.37C32.5,73.75,69,71.25,88.35,70.92c2.02-0.04,5.51,0.35,7.15,1"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0970e" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 24.50 12.50)">1</text>
	<text transform="matrix(1 0 0 1 13.50 22.50)">2</text>
	<text transform="matrix(1 0 0 1 21.50 19.50)">3</text>
	<text transform="matrix(1 0 0 1 46.50 18.50)">4</text>
	<text transform="matrix(1 0 0 1 26.50 29.50)">5</text>
	<text transform="matrix(1 0 0 1 21.50 35.50)">6</text>
	<text transform="matrix(1 0 0 1 59.50 27.50)">7</text>
	<text transform="matrix(1 0 0 1 57.50 35.50)">8</text>
	<text transform="matrix(1 0 0 1 44.50 40.50)">9</text>
	<text transform="matrix(1 0 0 1 12.50 48.50)">10</text>
	<text transform="matrix(1 0 0 1 28.50 55.50)">11</text>
	<text transform="matrix(1 0 0 1 60.50 52.50)">12</text>
	<text transform="matrix(1 0 0 1 4.50 63.50)">13</text>
	<text transform="matrix(1 0 0 1 39.50 67.50)">14</text>
	<text transform="matrix(1 0 0 1 68.50 67.50)">15</text>
	<text transform="matrix(1 0 0 1 5.50 74.50)">16</text>
</g>
</svg>
