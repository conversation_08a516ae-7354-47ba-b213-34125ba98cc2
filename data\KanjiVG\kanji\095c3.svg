<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_095c3" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:095c3" kvg:element="闃">
	<g id="kvg:095c3-g1" kvg:element="門" kvg:position="kamae" kvg:radical="general">
		<g id="kvg:095c3-g2" kvg:position="left">
			<path id="kvg:095c3-s1" kvg:type="㇑" d="M18.39,15.29c0.73,0.46,1.28,2.98,1.43,3.9c0.15,0.93-0.09,72.14-0.24,77.94"/>
			<path id="kvg:095c3-s2" kvg:type="㇕a" d="M20.02,16.75c2.36-0.09,18.68-1.85,20.83-1.95c1.79-0.09,2.72,0.78,2.81,1.46c0.13,1.05-1.49,19.06-1.49,19.58"/>
			<path id="kvg:095c3-s3" kvg:type="㇐a" d="M20.44,26.48c5.8-0.51,16.11-1.81,21.7-2.35"/>
			<path id="kvg:095c3-s4" kvg:type="㇐a" d="M20.02,36.73c7.86-0.98,13.95-1.73,20.9-2.18"/>
		</g>
		<g id="kvg:095c3-g3" kvg:position="right">
			<path id="kvg:095c3-s5" kvg:type="㇑" d="M64.44,14.01c0.44,0.55,0.88,1.74,0.88,2.48c0,0.74-0.15,19.04,0,19.78"/>
			<path id="kvg:095c3-s6" kvg:type="㇆a" d="M66.05,15.16c2.49-0.11,19.71-2.79,21.99-2.92c1.9-0.11,2.98,1.15,2.97,1.8c-0.49,19.46-0.5,64.29-0.5,78.4c0,6.88-3.14,5.32-5.31,3.25c-1.78-1.7-3.14-2.59-4.59-3.89"/>
			<path id="kvg:095c3-s7" kvg:type="㇐a" d="M65.49,24.72c3.38,0,21.09-1.74,24.9-1.74"/>
			<path id="kvg:095c3-s8" kvg:type="㇐a" d="M66.05,34.26c4.99,0,17.59-1.56,24.05-1.56"/>
		</g>
	</g>
	<g id="kvg:095c3-g4" kvg:element="目" kvg:position="top">
		<path id="kvg:095c3-s9" kvg:type="㇑" d="M38.66,42.5c0.45,0.51,1.1,0.89,1.1,1.57s1.7,22.2,1.7,22.54"/>
		<path id="kvg:095c3-s10" kvg:type="㇕a" d="M39.62,43.16c2.53-0.08,24.66-1.32,26.96-1.43c1.92-0.08,2.93,1.48,2.77,2.1c-0.2,0.8-0.94,17.36-0.94,20.44"/>
		<path id="kvg:095c3-s11" kvg:type="㇐a" d="M40.46,49.59c4.98-0.12,24.09-1.08,28.14-1.08"/>
		<path id="kvg:095c3-s12" kvg:type="㇐a" d="M42.16,56.44c6.69-0.28,19.64-1.52,26.13-1.52"/>
		<path id="kvg:095c3-s13" kvg:type="㇐a" d="M41.99,63.82c5.75-0.15,19.65-1.26,26.15-1.26"/>
	</g>
	<g id="kvg:095c3-g5" kvg:element="犬" kvg:position="bottom">
		<g id="kvg:095c3-g6" kvg:element="大">
			<path id="kvg:095c3-s14" kvg:type="㇐" d="M29.47,76.53c0.71,0.25,3.14,0.57,4.51,0.42c7.17-0.77,32-2.08,40.64-2.62c0.99-0.06,2.39,0.1,4.59,0.53"/>
			<path id="kvg:095c3-s15" kvg:type="㇒" d="M52.42,66.25c0.67,0.43,0.7,1.35,0.67,2.21C52.75,78.25,43.5,91.5,29.65,96.5"/>
			<path id="kvg:095c3-s16" kvg:type="㇏" d="M52.31,76.01C56.97,81.24,66.04,90.08,72,93.81c1.57,0.98,2.14,1.1,2.86,1.19"/>
		</g>
		<g id="kvg:095c3-g7" kvg:element="丶">
			<path id="kvg:095c3-s17" kvg:type="㇔" d="M61.54,67.12c3.66,1.55,4.66,2.74,5.79,4.19"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_095c3" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 12.50 23.50)">1</text>
	<text transform="matrix(1 0 0 1 20.50 13.50)">2</text>
	<text transform="matrix(1 0 0 1 24.50 24.50)">3</text>
	<text transform="matrix(1 0 0 1 24.33 34.50)">4</text>
	<text transform="matrix(1 0 0 1 57.50 21.50)">5</text>
	<text transform="matrix(1 0 0 1 71.25 11.25)">6</text>
	<text transform="matrix(1 0 0 1 68.75 22.18)">7</text>
	<text transform="matrix(1 0 0 1 68.71 32.60)">8</text>
	<text transform="matrix(1 0 0 1 32.50 48.50)">9</text>
	<text transform="matrix(1 0 0 1 43.50 41.50)">10</text>
	<text transform="matrix(1 0 0 1 44.50 48.50)">11</text>
	<text transform="matrix(1 0 0 1 44.50 54.50)">12</text>
	<text transform="matrix(1 0 0 1 44.25 62.50)">13</text>
	<text transform="matrix(1 0 0 1 27.25 74.50)">14</text>
	<text transform="matrix(1 0 0 1 43.50 71.50)">15</text>
	<text transform="matrix(1 0 0 1 60.50 83.50)">16</text>
	<text transform="matrix(1 0 0 1 69.25 70.50)">17</text>
</g>
</svg>
