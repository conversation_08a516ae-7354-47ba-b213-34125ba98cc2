<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0991d" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0991d" kvg:element="餝">
	<g id="kvg:0991d-g1" kvg:element="⻞" kvg:variant="true" kvg:original="食" kvg:position="left" kvg:radical="general">
		<path id="kvg:0991d-s1" kvg:type="㇒" d="M28.27,11.64c0.05,0.63,0.24,1.69-0.1,2.54c-2.18,5.59-8.47,16.54-17.37,24.78"/>
		<path id="kvg:0991d-s2" kvg:type="㇔/㇏" d="M30.27,17.58c4.07,1.88,10.52,7.72,11.54,10.63"/>
		<path id="kvg:0991d-s3" kvg:type="㇐" d="M21.25,32.16c0.36,0.15,1.02,0.19,1.39,0.15c2.3-0.25,10.87-1.67,13.1-1.5c0.6,0.05,0.96,0.07,1.27,0.14"/>
		<path id="kvg:0991d-s4" kvg:type="㇑" d="M18.27,39.95c0.39,0.8,0.78,1.7,0.78,2.77c0,1.06-0.13,55.21,0,56.28"/>
		<path id="kvg:0991d-s5" kvg:type="㇕" d="M19.19,42.11c2.2-0.13,17.91-2.71,19.92-2.87c1.67-0.13,2.74,1.47,2.61,2.26c-0.26,1.6-1.97,19.33-2.52,22.87"/>
		<path id="kvg:0991d-s6" kvg:type="㇐a" d="M19.58,51.94c2.97,0,17.86-1.85,21.22-1.85"/>
		<path id="kvg:0991d-s7" kvg:type="㇐a" d="M19.44,63.97c6.06-0.57,12.31-1.1,19.85-1.59"/>
		<path id="kvg:0991d-s8" kvg:type="㇐c" d="M18.89,76.67c3.73-0.26,15.46-2.21,19.07-2.04c0.97,0.05,1.56,0.07,2.05,0.14"/>
		<path id="kvg:0991d-s9" kvg:type="㇐c" d="M18.89,90.15c3.73-0.26,16.46-2.2,20.07-2.03c0.97,0.05,1.56,0.07,2.05,0.15"/>
	</g>
	<g id="kvg:0991d-g2" kvg:element="芳" kvg:position="right">
		<g id="kvg:0991d-g3" kvg:element="艹" kvg:variant="true" kvg:original="艸" kvg:position="top">
			<path id="kvg:0991d-s10" kvg:type="㇐" d="M47.44,27.51c0.94,0.32,2.04,0.38,2.98,0.32c8.65-0.58,35.19-3.28,45.17-3.32c1.57-0.01,2.51,0.15,3.3,0.31"/>
			<path id="kvg:0991d-s11" kvg:type="㇑a" d="M57.24,13c1.51,1.25,2.32,2.12,2.48,3.13C61.25,26,61.92,29.88,62.81,37"/>
			<path id="kvg:0991d-s12" kvg:type="㇑a" d="M83.56,12.25c0.69,1.25,1.07,2.53,0.87,4C83.5,23,83,27.25,82.16,35"/>
		</g>
		<g id="kvg:0991d-g4" kvg:element="方" kvg:position="bottom">
			<g id="kvg:0991d-g5" kvg:element="亠" kvg:position="top">
				<path id="kvg:0991d-s13" kvg:type="㇑a" d="M69.92,33.27c0.7,0.51,1.85,2.5,1.85,3.53c0,3.77-0.22,7.1-0.09,10.29"/>
				<path id="kvg:0991d-s14" kvg:type="㇐" d="M48.5,49.12c1.05,0.1,3.43,0.69,4.45,0.55c8.22-1.16,30.5-3.41,41.66-4.2c1.72-0.12,2.6,0.05,3.89,0.7"/>
			</g>
			<g id="kvg:0991d-g6" kvg:position="bottom">
				<path id="kvg:0991d-s15" kvg:type="㇆a" d="M71.26,55.52c4.99,1.98,9.49,3.73,15.07,5.44c2.64,0.81,3.01,1.71,2.86,3.59c-0.4,4.96-10.52,26.79-13.96,30.82C71.5,99.74,69.71,95,69,92.61"/>
				<path id="kvg:0991d-s16" kvg:type="㇒" d="M69.9,51.27c0.07,0.96,0.29,2.52-0.14,3.86c-2.94,9.2-11.08,28.19-24.28,36.89"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0991d" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 21.50 11.50)">1</text>
	<text transform="matrix(1 0 0 1 33.50 17.50)">2</text>
	<text transform="matrix(1 0 0 1 26.50 28.50)">3</text>
	<text transform="matrix(1 0 0 1 11.25 47.40)">4</text>
	<text transform="matrix(1 0 0 1 20.50 39.50)">5</text>
	<text transform="matrix(1 0 0 1 23.46 49.50)">6</text>
	<text transform="matrix(1 0 0 1 23.50 61.50)">7</text>
	<text transform="matrix(1 0 0 1 23.47 73.50)">8</text>
	<text transform="matrix(1 0 0 1 23.46 86.50)">9</text>
	<text transform="matrix(1 0 0 1 44.50 24.50)">10</text>
	<text transform="matrix(1 0 0 1 50.50 10.50)">11</text>
	<text transform="matrix(1 0 0 1 75.50 9.50)">12</text>
	<text transform="matrix(1 0 0 1 62.50 44.50)">13</text>
	<text transform="matrix(1 0 0 1 47.50 46.50)">14</text>
	<text transform="matrix(1 0 0 1 75.50 55.50)">15</text>
	<text transform="matrix(1 0 0 1 58.50 59.50)">16</text>
</g>
</svg>
