<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0971e" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0971e" kvg:element="霞">
	<g id="kvg:0971e-g1" kvg:element="雨" kvg:variant="true" kvg:position="top" kvg:radical="general">
		<path id="kvg:0971e-s1" kvg:type="㇐" d="M35.69,14.53c2.36,0.27,4.47,0.16,6.81-0.13c5.22-0.65,14.4-1.59,23.75-2.16c2.17-0.13,4.34-0.3,6.51-0.01"/>
		<path id="kvg:0971e-s2" kvg:type="㇔/㇑" d="M21.53,24.6c-0.21,4.39-2.08,9.76-3.47,14.19"/>
		<path id="kvg:0971e-s3" kvg:type="㇖b/㇆" d="M22.38,26.98c20.27-2.55,49.37-5.76,64.53-5.76c7.84,0,3.04,3.76-1.02,7.08"/>
		<path id="kvg:0971e-s4" kvg:type="㇑" d="M52.72,14.99c0.77,0.77,0.99,1.76,0.99,3.06c0,0.21,0,10.67,0,17.71c0,3.5,0,6.16,0,6.29"/>
		<path id="kvg:0971e-s5" kvg:type="㇔" d="M33.25,30.5c2.98,0.56,7.04,2.22,8.66,3.15"/>
		<path id="kvg:0971e-s6" kvg:type="㇔" d="M31.37,38.18c2.71,0.66,6.9,3.11,8.37,4.21"/>
		<path id="kvg:0971e-s7" kvg:type="㇔" d="M67.57,27.79c3.2,0.87,6.83,2.61,8.11,3.33"/>
		<path id="kvg:0971e-s8" kvg:type="㇔" d="M66.18,36.06c2.67,0.66,6.32,2.66,7.78,3.77"/>
	</g>
	<g id="kvg:0971e-g2" kvg:element="叚" kvg:position="bottom">
		<g id="kvg:0971e-g3" kvg:position="left">
			<path id="kvg:0971e-s9" kvg:type="㇕" d="M21.9,50.64c1.19-0.14,2.51,0.44,3.91,0.24c5.94-0.86,13.38-2.38,18.93-2.98c3.38-0.36,3.86,0.58,2.83,3.46c-1.06,2.98-1.69,5.7-2.21,8.18"/>
			<path id="kvg:0971e-s10" kvg:type="㇐" d="M25.01,63.28c5.24-0.78,11.57-1.77,17.47-2.26c1.87-0.16,3.52-0.29,4.76-0.37"/>
			<path id="kvg:0971e-s11" kvg:type="㇑" d="M22.89,51.48c0.69,0.69,1.15,1.62,1.15,2.69c0,0.81,0.02,24.83,0,37.33C24.03,95.6,24,96.38,24,98.75"/>
			<path id="kvg:0971e-s12" kvg:type="㇐" d="M25.26,73.85c3.99-0.26,9.85-1.15,14.85-1.68c1.74-0.18,3.48-0.38,5.23-0.12"/>
			<path id="kvg:0971e-s13" kvg:type="㇐" d="M25.13,86.52c3.76-0.25,11.19-1.05,16.36-1.58c2.08-0.22,3.51-0.19,5.46,0.03"/>
		</g>
		<g id="kvg:0971e-g4" kvg:position="right">
			<path id="kvg:0971e-s14" kvg:type="㇕" d="M57.84,47.13c1.54,0.62,3.58,0.68,4.86,0.61c5.69-0.35,14.19-1.98,19.94-2.5c2.61-0.24,3.61,1.26,3.12,3.29c-0.58,2.37-1.02,3.99-1.92,7.4"/>
			<path id="kvg:0971e-s15" kvg:type="㇐" d="M57.45,59.17c1.77,0.79,3.55,0.59,5.43,0.42c4.88-0.43,12.6-1.51,18.11-2.06c2.37-0.24,4.26-0.41,5.49-0.25"/>
			<g id="kvg:0971e-g5" kvg:element="又">
				<path id="kvg:0971e-s16" kvg:type="㇇" d="M57.02,68.71c1.94,0.28,3.62,0.32,5.6,0c4.06-0.66,14.02-2.4,17.1-2.92c3-0.5,4.6,1.05,2.9,3.42C74.38,80.75,64,90.88,50.1,96.25"/>
				<path id="kvg:0971e-s17" kvg:type="㇏" d="M56.9,74.25c3.18,0.46,16.6,9.87,28.85,17.87c2.57,1.68,4.38,2.38,7.42,3.51"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0971e" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 30.25 15.25)">1</text>
	<text transform="matrix(1 0 0 1 15.00 26.30)">2</text>
	<text transform="matrix(1 0 0 1 24.00 23.23)">3</text>
	<text transform="matrix(1 0 0 1 46.25 21.25)">4</text>
	<text transform="matrix(1 0 0 1 26.25 33.25)">5</text>
	<text transform="matrix(1 0 0 1 24.75 39.25)">6</text>
	<text transform="matrix(1 0 0 1 61.25 30.25)">7</text>
	<text transform="matrix(1 0 0 1 59.25 38.25)">8</text>
	<text transform="matrix(1 0 0 1 26.00 48.25)">9</text>
	<text transform="matrix(1 0 0 1 29.00 60.45)">10</text>
	<text transform="matrix(1 0 0 1 14.25 58.13)">11</text>
	<text transform="matrix(1 0 0 1 29.06 70.25)">12</text>
	<text transform="matrix(1 0 0 1 29.11 83.25)">13</text>
	<text transform="matrix(1 0 0 1 58.25 45.25)">14</text>
	<text transform="matrix(1 0 0 1 58.25 57.25)">15</text>
	<text transform="matrix(1 0 0 1 59.25 66.25)">16</text>
	<text transform="matrix(1 0 0 1 63.25 76.25)">17</text>
</g>
</svg>
