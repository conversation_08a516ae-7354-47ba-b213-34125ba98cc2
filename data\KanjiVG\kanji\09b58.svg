<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09b58" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09b58" kvg:element="魘">
	<g id="kvg:09b58-g1" kvg:element="厭" kvg:position="tare">
		<g id="kvg:09b58-g2" kvg:element="厂" kvg:position="tare">
			<path id="kvg:09b58-s1" kvg:type="㇐" d="M19.34,13.93c1.75,0.43,4.95,0.5,6.7,0.43c18.19-0.72,38.76-3.32,56.39-3.88c2.91-0.09,4.66,0.2,6.12,0.42"/>
			<g id="kvg:09b58-g3" kvg:element="丿" kvg:radical="nelson">
				<path id="kvg:09b58-s2" kvg:type="㇒" d="M22.57,15.08c0.47,1.64,0.48,2.08,0.4,4.76c-0.64,21.45-2.29,55.57-12.41,73.83"/>
			</g>
		</g>
		<g id="kvg:09b58-g4" kvg:position="left">
			<g id="kvg:09b58-g5" kvg:element="日" kvg:position="top">
				<path id="kvg:09b58-s3" kvg:type="㇑" d="M31.76,19.79c0.27,0.27,0.46,0.7,0.53,1.06c0.26,1.21,2.2,10.52,2.2,10.7"/>
				<path id="kvg:09b58-s4" kvg:type="㇕a" d="M32.73,20.49c1.5-0.05,17.62-1.92,18.99-1.98c1.14-0.04,1.88,1.36,1.79,1.62c-0.18,0.54-1.06,9.66-1.06,9.93"/>
				<path id="kvg:09b58-s5" kvg:type="㇐a" d="M33.39,25.45c2.03,0,16.93-1.8,19.23-1.8"/>
				<path id="kvg:09b58-s6" kvg:type="㇐a" d="M35.11,30.55c4.87-0.15,11.84-1.34,16.54-1.42"/>
			</g>
			<g id="kvg:09b58-g6" kvg:element="月" kvg:position="bottom">
				<path id="kvg:09b58-s7" kvg:type="㇑" d="M31.46,37.52c0.29,0.29,0.58,0.81,0.58,1.1c0,2.02,0.1,16.01,0.1,18.24"/>
				<path id="kvg:09b58-s8" kvg:type="㇆a" d="M32.61,37.81c1.15-0.07,18.86-1.93,19.72-2.01c1.54-0.15,2.11,0.94,1.92,1.37c-0.19,0.42-0.29,8.91-0.29,14.76c0,4.95-2.79,1.73-4.3,0.65"/>
				<path id="kvg:09b58-s9" kvg:type="㇐a" d="M32.61,43.28c3.62-0.14,17.51-1.72,20.97-1.94"/>
				<path id="kvg:09b58-s10" kvg:type="㇐a" d="M32.34,48.24c3.08-0.22,17.49-1.53,21.05-1.67"/>
			</g>
		</g>
		<g id="kvg:09b58-g7" kvg:element="犬" kvg:position="right">
			<g id="kvg:09b58-g8" kvg:element="大">
				<path id="kvg:09b58-s11" kvg:type="㇐" d="M57.68,30.1c0.52,0.18,2.29,0.39,3.29,0.29c5.25-0.54,19-1.4,25.32-1.77c0.72-0.04,1.75,0.07,3.36,0.37"/>
				<path id="kvg:09b58-s12" kvg:type="㇒" d="M71.09,18.2c0.3,0.47,0.58,0.94,0.51,1.87C70.75,32.5,69,44.5,60.78,51.84"/>
				<path id="kvg:09b58-s13" kvg:type="㇏" d="M71.04,30.12c4.03,6.44,11.88,17.34,17.03,21.93c1.35,1.21,1.85,1.36,2.47,1.47"/>
			</g>
			<g id="kvg:09b58-g9" kvg:element="丶">
				<path id="kvg:09b58-s14" kvg:type="㇔" d="M78.19,17.84c4.54,1.83,5.79,3.23,7.19,4.95"/>
			</g>
		</g>
	</g>
	<g id="kvg:09b58-g10" kvg:element="鬼" kvg:position="tarec" kvg:radical="tradit">
		<g id="kvg:09b58-g11" kvg:element="丿">
			<path id="kvg:09b58-s15" kvg:type="㇒" d="M54.37,56.75c0.02,0.15,0.06,0.43-0.03,0.6c-1.12,2.11-2.77,4.04-5.68,5.74"/>
		</g>
		<g id="kvg:09b58-g12" kvg:element="田">
			<path id="kvg:09b58-s16" kvg:type="㇑" d="M33.2,64.76c0.22,0.32,0.22,0.53,0.34,0.92c0.96,3.21,2.64,9.75,3.21,15.66"/>
			<path id="kvg:09b58-s17" kvg:type="㇕a" d="M34.22,65.53c7.63-0.32,37.16-3.32,42.85-3.64c2.09-0.12,2.95,0.92,2.72,2.25c-0.53,3.13-2.09,10.07-3.94,15.68"/>
			<path id="kvg:09b58-s18" kvg:type="㇑a" d="M 55.350416,65.1142 c 0.67,0.83 0.79,1.58 0.79,2.67 0,2.05 -0.09,6.823971 -0.09,9.943971"/>
			<path id="kvg:09b58-s19" kvg:type="㇐a" d="M36.08,72.38c2.48-0.16,38.84-2.72,42.32-3.04"/>
			<path id="kvg:09b58-s20" kvg:type="㇐a" d="M37.14,79.71C46,79,67.44,77.82,75.7,76.95"/>
		</g>
		<g id="kvg:09b58-g13" kvg:element="儿" kvg:variant="true" kvg:original="八">
			<g id="kvg:09b58-g14" kvg:element="丿" kvg:position="left">
				<path id="kvg:09b58-s21" kvg:type="㇒" d="M47.38,82.68c0.13,0.68-0.15,1.42-0.62,1.96C43.48,88.35,35.58,95,20.75,99"/>
			</g>
			<g id="kvg:09b58-g15" kvg:position="right">
				<path id="kvg:09b58-s22" kvg:type="㇟" d="M57.86,80.21c0.7,0.61,1.26,1.33,1.3,2.26c0.19,4.19,0.19,2.9,0.19,8.59c0,6.68,2.82,7.28,17.99,7.28S94.25,97,94.25,89.27"/>
			</g>
		</g>
		<g id="kvg:09b58-g16" kvg:element="厶">
			<path id="kvg:09b58-s23" kvg:type="㇜" d="M72.15,81.94c0.24,0.21,0.15,1.19-0.08,1.45c-2.05,2.4-2.13,4.08-5.38,6.52c-1.03,0.77,0.08,1.55,1.54,1.29c5.09-0.92,9.38-1.82,14.32-2.78"/>
			<path id="kvg:09b58-s24" kvg:type="㇔" d="M79.78,85.16c1.37,0.86,4.39,4.27,4.73,5.62"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09b58" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 22.50 10.50)">1</text>
	<text transform="matrix(1 0 0 1 15.50 22.50)">2</text>
	<text transform="matrix(1 0 0 1 26.50 25.50)">3</text>
	<text transform="matrix(1 0 0 1 34.50 19.50)">4</text>
	<text transform="matrix(1 0 0 1 38.50 24.50)">5</text>
	<text transform="matrix(1 0 0 1 38.50 29.25)">6</text>
	<text transform="matrix(1 0 0 1 25.25 43.50)">7</text>
	<text transform="matrix(1 0 0 1 35.50 36.50)">8</text>
	<text transform="matrix(1 0 0 1 36.50 42.03)">9</text>
	<text transform="matrix(1 0 0 1 35.50 46.95)">10</text>
	<text transform="matrix(1 0 0 1 55.50 27.50)">11</text>
	<text transform="matrix(1 0 0 1 61.50 19.50)">12</text>
	<text transform="matrix(1 0 0 1 77.50 36.73)">13</text>
	<text transform="matrix(1 0 0 1 84.25 18.50)">14</text>
	<text transform="matrix(1 0 0 1 44.50 59.50)">15</text>
	<text transform="matrix(1 0 0 1 23.50 68.50)">16</text>
	<text transform="matrix(1 0 0 1 35.50 63.50)">17</text>
	<text transform="matrix(1 0 0 1 59.25 68.35)">18</text>
	<text transform="matrix(1 0 0 1 38.50 70.50)">19</text>
	<text transform="matrix(1 0 0 1 40.25 78.50)">20</text>
	<text transform="matrix(1 0 0 1 34.50 89.50)">21</text>
	<text transform="matrix(1 0 0 1 49.75 91.50)">22</text>
	<text transform="matrix(1 0 0 1 62.50 85.50)">23</text>
	<text transform="matrix(1 0 0 1 80.50 83.13)">24</text>
</g>
</svg>
