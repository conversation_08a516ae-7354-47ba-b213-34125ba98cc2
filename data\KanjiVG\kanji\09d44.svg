<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09d44" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09d44" kvg:element="鵄">
	<g id="kvg:09d44-g1" kvg:element="至" kvg:position="left" kvg:radical="nelson">
		<path id="kvg:09d44-s1" kvg:type="㇐" d="M12,22c0.84,0.43,2.38,0.48,3.23,0.43C22.5,22,33,20.75,42.81,20.35c1.4-0.06,2.24,0.2,2.95,0.42"/>
		<g id="kvg:09d44-g2" kvg:element="厶">
			<path id="kvg:09d44-s2" kvg:type="㇜" d="M24.67,25.25c0.36,0.43,0.45,1.77,0.21,2.29c-2.38,5.21-6.16,10.57-10.48,16.19c-1.39,1.81-1.49,3.03,0.74,2.5c5.21-1.25,17.42-4.58,25.64-6.64"/>
			<path id="kvg:09d44-s3" kvg:type="㇔" d="M35.9,30.24c3.44,2.78,8.88,11.45,9.74,15.78"/>
		</g>
		<g id="kvg:09d44-g3" kvg:element="土">
			<path id="kvg:09d44-s4" kvg:type="㇐" d="M12.19,62.87c1.06,0.13,3.15,0.29,3.74,0.25c6.82-0.38,20.32-2.13,26.6-2.98c0.97-0.13,3.06,0.12,3.55,0.25"/>
			<path id="kvg:09d44-s5" kvg:type="㇑" d="M26.69,45.28c0.12,0.33,1.78,1.7,1.78,3.78c0,10.44,0.06,30.44,0.06,33.83"/>
			<path id="kvg:09d44-s6" kvg:type="㇀/㇐" d="M13.35,89.15c1.1,0.86,3.01,0.62,3.87,0.12c8.68-4.95,16.09-8.3,28.01-15.15"/>
		</g>
	</g>
	<g id="kvg:09d44-g4" kvg:element="鳥" kvg:position="right" kvg:radical="tradit">
		<path id="kvg:09d44-s7" kvg:type="㇒" d="M68.54,10.66c0.02,0.28,0.04,0.72-0.03,1.11c-0.41,2.32-2.74,7.29-5.93,10.18"/>
		<path id="kvg:09d44-s8" kvg:type="㇑" d="M55.8,23.42c0.33,0.43,0.6,1.02,0.6,1.73c0,7.23,0.04,33.51-0.17,44.88"/>
		<path id="kvg:09d44-s9" kvg:type="㇕a" d="M56.81,24.75c1.55,0,21-3.17,22.51-3.06c2.24,0.16,3.33,2.15,3.13,4.02c-0.12,1.15-0.64,13.19-2.08,21.96"/>
		<path id="kvg:09d44-s10" kvg:type="㇐a" d="M57.06,36.08c1.9,0.13,21.6-2.74,23.75-2.59"/>
		<path id="kvg:09d44-s11" kvg:type="㇐a" d="M56.97,48.12c3.88-0.14,18.55-2.48,23.38-2.5"/>
		<path id="kvg:09d44-s12" kvg:type="㇐b" d="M56.69,59.3c8.97-1.11,29.32-3.75,33.17-4.44c1.34-0.24,3.62-0.46,4.3-0.14"/>
		<path id="kvg:09d44-s13" kvg:type="㇆a" d="M56.24,70.25c9.1-1.63,28.63-3.42,33.67-3.9c3.28-0.31,4.97,0.89,4.38,4.57C92.65,81.16,89.25,89,85.86,94.67c-3.56,5.96-4.86,3.08-7.3-0.24"/>
		<g id="kvg:09d44-g5" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09d44-s14" kvg:type="㇔" d="M51.1,80.23c-0.1,4.02-2.1,9.02-4.19,12.94"/>
			<path id="kvg:09d44-s15" kvg:type="㇔" d="M57.39,80.24c1.78,1.89,3.46,6.85,3.91,9.62"/>
			<path id="kvg:09d44-s16" kvg:type="㇔" d="M68.2,78.29c1.34,1.42,3.46,5.68,3.8,7.76"/>
			<path id="kvg:09d44-s17" kvg:type="㇔" d="M76.16,74.73c1.71,1.49,4.43,5.96,4.86,8.14"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09d44" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 5.50 22.50)">1</text>
	<text transform="matrix(1 0 0 1 14.50 32.50)">2</text>
	<text transform="matrix(1 0 0 1 38.50 30.48)">3</text>
	<text transform="matrix(1 0 0 1 5.50 62.50)">4</text>
	<text transform="matrix(1 0 0 1 20.50 54.50)">5</text>
	<text transform="matrix(1 0 0 1 4.50 88.50)">6</text>
	<text transform="matrix(1 0 0 1 60.50 10.50)">7</text>
	<text transform="matrix(1 0 0 1 48.25 30.50)">8</text>
	<text transform="matrix(1 0 0 1 56.50 21.50)">9</text>
	<text transform="matrix(1 0 0 1 59.50 33.50)">10</text>
	<text transform="matrix(1 0 0 1 59.50 45.50)">11</text>
	<text transform="matrix(1 0 0 1 59.50 56.50)">12</text>
	<text transform="matrix(1 0 0 1 59.50 67.23)">13</text>
	<text transform="matrix(1 0 0 1 40.50 84.50)">14</text>
	<text transform="matrix(1 0 0 1 51.25 89.50)">15</text>
	<text transform="matrix(1 0 0 1 60.50 84.50)">16</text>
	<text transform="matrix(1 0 0 1 71.50 81.50)">17</text>
</g>
</svg>
