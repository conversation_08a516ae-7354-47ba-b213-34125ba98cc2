<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09b31" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09b31" kvg:element="鬱">
	<g id="kvg:09b31-g1" kvg:position="top">
		<g id="kvg:09b31-g2" kvg:element="缶">
			<path id="kvg:09b31-s1" kvg:type="㇒" d="M48.3,10.89c0.02,0.26,0.04,0.66-0.04,1.03c-0.49,2.16-3.33,6.91-7.22,9.82"/>
			<path id="kvg:09b31-s2" kvg:type="㇐" d="M47.11,16.35c0.31,0.13,0.88,0.15,1.19,0.13c3.8-0.35,9.58-1.17,12.99-1.61c0.51-0.07,0.83,0.06,1.08,0.12"/>
			<path id="kvg:09b31-s3" kvg:type="㇐" d="M40.26,24.54c0.5,0.18,1.42,0.21,1.92,0.18c5.22-0.3,15.2-1.82,22.82-1.89c0.83-0.01,1.34,0.08,1.75,0.17"/>
			<path id="kvg:09b31-s4" kvg:type="㇑" d="M53.11,16.58c0.31,0.45,0.66,0.78,0.66,1.4c0,2.18-0.03,15.57-0.08,17.41"/>
			<path id="kvg:09b31-s5" kvg:type="㇄" d="M44.2,29.3c0.25,0.15,0.5,0.77,0.5,1.07c-0.02,1.85-0.02,1.1,0,4.61c0,0.68-0.12,1.34,0.5,1.24c1.71-0.29,13.7-1.9,16.84-1.99"/>
			<path id="kvg:09b31-s6" kvg:type="㇑" d="M63.35,27.91c0.25,0.15,0.54,1.11,0.5,1.4c-0.25,1.82-0.5,3.19-0.91,6.57"/>
		</g>
		<g id="kvg:09b31-g3" kvg:element="木" kvg:radical="nelson">
			<path id="kvg:09b31-s7" kvg:type="㇐" d="M14.87,19.27c0.52,0.09,2.1,0.08,3.49,0c4.88-0.26,11.48-0.71,16.71-0.88c0.89-0.03,1.94-0.31,3.31-0.18"/>
			<path id="kvg:09b31-s8" kvg:type="㇑" d="M28.06,10.34c0.59,0.23,0.94,1.06,1.06,1.53c0.12,0.47,0,23.55-0.12,26.49"/>
			<path id="kvg:09b31-s9" kvg:type="㇒" d="M29.4,20.03c-2.67,4.49-10.61,12.03-16.15,14.57"/>
			<path id="kvg:09b31-s10" kvg:type="㇏" d="M31.28,23.22c2.12,1.2,4.39,3.61,5.6,5.72"/>
		</g>
		<g id="kvg:09b31-g4" kvg:element="木">
			<path id="kvg:09b31-s11" kvg:type="㇐" d="M68.83,18.29c0.33,0.12,1.06,0.18,1.95,0.12c3.15-0.24,12.6-1.06,18.75-1.65c0.89-0.08,1.67-0.12,2.23,0"/>
			<path id="kvg:09b31-s12" kvg:type="㇑" d="M77.1,10.4c0.59,0.23,0.94,1.06,1.06,1.53c0.12,0.47,0,21.26-0.12,24.2"/>
			<path id="kvg:09b31-s13" kvg:type="㇒" d="M78.07,18.62c-1.53,4.22-6.28,10.08-9.83,12.25"/>
			<path id="kvg:09b31-s14" kvg:type="㇏" d="M78.02,18.54c3.84,4.51,9.99,10.29,13.1,12.07c0.89,0.51,1.39,0.86,2.13,1.03"/>
		</g>
		<g id="kvg:09b31-g5" kvg:element="冖">
			<path id="kvg:09b31-s15" kvg:type="㇔" d="M19.16,41.13c0,2.91-3.72,10.92-5.41,12.87"/>
			<path id="kvg:09b31-s16" kvg:type="㇆" d="M18.99,44.24c9.51-0.99,60.24-3.03,67.95-3.4C99,40.25,89.5,48,86.5,50.11"/>
		</g>
	</g>
	<g id="kvg:09b31-g6" kvg:position="bottom">
		<g id="kvg:09b31-g7" kvg:element="鬯" kvg:position="left" kvg:radical="tradit">
			<g id="kvg:09b31-g8" kvg:position="top">
				<path id="kvg:09b31-s17" kvg:type="㇒" d="M44.91,49.53c0.05,0.47,0.27,1.29-0.1,1.9C42.5,55.25,34.5,65,27.1,69.61"/>
				<path id="kvg:09b31-s18" kvg:type="㇔" d="M29.57,52.91c5.7,2.95,14.71,12.14,16.14,16.74"/>
				<path id="kvg:09b31-s19" kvg:type="㇔" d="M33.78,47.58c1.41,0.79,3.63,3.24,3.99,4.47"/>
				<path id="kvg:09b31-s20" kvg:type="㇔" d="M26.71,56.64c1.41,0.88,3.63,3.6,3.99,4.97"/>
				<path id="kvg:09b31-s21" kvg:type="㇔" d="M45.7,58.3c1.14,0.79,2.95,3.24,3.24,4.47"/>
				<path id="kvg:09b31-s22" kvg:type="㇔" d="M35.51,65.78c1.19,0.56,3.07,2.31,3.37,3.18"/>
				<g id="kvg:09b31-g9" kvg:element="凵">
					<path id="kvg:09b31-s23" kvg:type="㇄a" d="M19.46,53.75c0.32,0.2,0.86,0.97,0.86,1.94c0,0.41,1.67,16.44,1.64,17.41c-0.03,0.96,0.29,1.65,1.35,1.54c5.9-0.57,25.29-1.48,28.67-1.68"/>
					<path id="kvg:09b31-s24" kvg:type="㇑" d="M52.64,51.99c0.62,0.28,1.08,0.85,1.08,1.94c0,2.18-0.21,11.43-0.95,21.2"/>
				</g>
			</g>
			<g id="kvg:09b31-g10" kvg:element="匕" kvg:variant="true" kvg:position="bottom">
				<path id="kvg:09b31-s25" kvg:type="㇒" d="M46.74,79.93c0.17,0.18,0.28,0.72-0.17,1c-2.9,1.83-12.86,6.87-22.25,8.73"/>
				<path id="kvg:09b31-s26" kvg:type="㇟" d="M21.46,79.82c0.59,0.56,0.63,0.93,0.83,1.67c0.2,0.75-0.04,8.93-0.04,11.26c0,5.99,7.88,5.06,14.59,5.06c5.2,0,9.54-0.32,11.5-1.99c1.96-1.67,1.72-3.62,1.92-5.3"/>
			</g>
		</g>
		<g id="kvg:09b31-g11" kvg:element="彡" kvg:position="right">
			<path id="kvg:09b31-s27" kvg:type="㇒" d="M80.11,50.75c0.06,0.34,0.22,0.92-0.12,1.36C77,56,71.25,60.25,59.75,64.63"/>
			<path id="kvg:09b31-s28" kvg:type="㇒" d="M85.67,64.39c0.08,0.4,0.28,1.09-0.15,1.61c-2.95,3.51-16.27,11.5-27.03,15.37"/>
			<path id="kvg:09b31-s29" kvg:type="㇒" d="M91.3,79c0.1,0.47,0.2,1.22-0.18,1.9C88.85,84.89,75.86,93.64,58.07,99"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09b31" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 41.25 11.50)">1</text>
	<text transform="matrix(1 0 0 1 52.50 13.50)">2</text>
	<text transform="matrix(1 0 0 1 47.50 23.50)">3</text>
	<text transform="matrix(1 0 0 1 56.50 22.10)">4</text>
	<text transform="matrix(1 0 0 1 38.25 34.50)">5</text>
	<text transform="matrix(1 0 0 1 55.50 31.50)">6</text>
	<text transform="matrix(1 0 0 1 8.50 19.50)">7</text>
	<text transform="matrix(1 0 0 1 20.25 10.50)">8</text>
	<text transform="matrix(1 0 0 1 19.25 27.48)">9</text>
	<text transform="matrix(1 0 0 1 35.50 25.50)">10</text>
	<text transform="matrix(1 0 0 1 66.25 16.50)">11</text>
	<text transform="matrix(1 0 0 1 72.25 7.50)">12</text>
	<text transform="matrix(1 0 0 1 66.25 25.73)">13</text>
	<text transform="matrix(1 0 0 1 85.25 24.50)">14</text>
	<text transform="matrix(1 0 0 1 8.25 43.58)">15</text>
	<text transform="matrix(1 0 0 1 20.50 40.50)">16</text>
	<text transform="matrix(1 0 0 1 36.50 49.35)">17</text>
	<text transform="matrix(1 0 0 1 21.25 53.50)">18</text>
	<text transform="matrix(1 0 0 1 27.50 51.20)">19</text>
	<text transform="matrix(1 0 0 1 23.50 63.13)">20</text>
	<text transform="matrix(1 0 0 1 44.75 57.50)">21</text>
	<text transform="matrix(1 0 0 1 30.50 72.50)">22</text>
	<text transform="matrix(1 0 0 1 10.25 62.43)">23</text>
	<text transform="matrix(1 0 0 1 50.50 49.50)">24</text>
	<text transform="matrix(1 0 0 1 35.75 81.13)">25</text>
	<text transform="matrix(1 0 0 1 10.75 82.13)">26</text>
	<text transform="matrix(1 0 0 1 70.50 50.50)">27</text>
	<text transform="matrix(1 0 0 1 74.50 65.50)">28</text>
	<text transform="matrix(1 0 0 1 79.50 81.63)">29</text>
</g>
</svg>
