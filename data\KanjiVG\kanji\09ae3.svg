<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09ae3" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09ae3" kvg:element="髣">
	<g id="kvg:09ae3-g1" kvg:element="髟" kvg:position="top" kvg:radical="general">
		<g id="kvg:09ae3-g2" kvg:element="長" kvg:variant="true" kvg:position="left">
			<path id="kvg:09ae3-s1" kvg:type="㇑a" d="M25.5,12.56c1.39,1.02,1.39,1.19,1.39,2.21s-0.04,21.07-0.04,22.6"/>
			<path id="kvg:09ae3-s2" kvg:type="㇐b" d="M27.94,14.08C32.71,13.9,41,12.5,47.1,11.28c1.42-0.28,2.32,0.12,3.05,0.25"/>
			<path id="kvg:09ae3-s3" kvg:type="㇐b" d="M27.52,22.39c4.22-0.36,13.11-2.16,16.52-2.52c1.28-0.13,2.58-0.09,3.57,0.12"/>
			<path id="kvg:09ae3-s4" kvg:type="㇐b" d="M27.28,29.88c4.22-0.36,14.11-2.66,17.52-3.02c1.28-0.13,2.58-0.09,3.57,0.12"/>
			<path id="kvg:09ae3-s5" kvg:type="㇐" d="M11.88,39.52c0.79,0.21,2.26,0.31,3.05,0.21C24.25,38.5,41,35.25,51.7,34.74c1.32-0.06,2.12,0.1,2.78,0.2"/>
			<g id="kvg:09ae3-g3" kvg:element="厶">
				<path id="kvg:09ae3-s6" kvg:type="㇜" d="M28.74,40.62c0.08,0.58-0.36,1.43-0.6,1.73c-3.29,3.95-4.23,5.18-8.77,9.59c-1.13,1.11-0.54,2.95,1.39,2.4c7.49-2.12,17.76-4.99,26.29-7.73"/>
				<path id="kvg:09ae3-s7" kvg:type="㇔" d="M42.34,42.23c2.64,1.52,6.83,6.24,7.49,8.6"/>
			</g>
		</g>
		<g id="kvg:09ae3-g4" kvg:element="彡" kvg:position="right">
			<path id="kvg:09ae3-s8" kvg:type="㇒" d="M80.5,9.5c0.05,0.34,0.24,0.92-0.11,1.35c-2.68,3.32-8.62,8.14-19.33,12.46"/>
			<path id="kvg:09ae3-s9" kvg:type="㇒" d="M85.62,21.28c0.07,0.37,0.32,1.03-0.13,1.47c-3.42,3.36-11.51,9.84-23.66,14.1"/>
			<path id="kvg:09ae3-s10" kvg:type="㇒" d="M90.09,32.79c0.08,0.39,0.29,1.03-0.15,1.56c-2.98,3.6-12.35,10.49-26.76,14.9"/>
		</g>
	</g>
	<g id="kvg:09ae3-g5" kvg:element="方" kvg:position="bottom">
		<g id="kvg:09ae3-g6" kvg:element="亠" kvg:position="top">
			<path id="kvg:09ae3-s11" kvg:type="㇑a" d="M54.09,54.02c0.76,0.41,1.52,2.01,1.52,2.82c0,0.91-0.1,2.91-0.1,4.74"/>
			<path id="kvg:09ae3-s12" kvg:type="㇐" d="M16,65.62c1.83,0.39,5.72,0.19,7.41,0.05c13.68-1.16,46.28-4.91,64.87-5.7c2.87-0.12,4.33,0.05,6.47,0.7"/>
		</g>
		<g id="kvg:09ae3-g7" kvg:position="bottom">
			<path id="kvg:09ae3-s13" kvg:type="㇆a" d="M53.58,69.52c1.17,0.23,2.34,0.62,3.18,0.72c4.74,0.52,9.61,1.39,13.38,1.12c2.89-0.21,3.8,2.95,3.53,4.38c-0.91,4.77-5.41,15.02-9.49,19.57c-3.45,3.84-6.92,0.45-7.69-2.08"/>
			<path id="kvg:09ae3-s14" kvg:type="㇒" d="M51.57,64.77c0.39,0.9,0.31,1.91-0.15,2.91C48.27,74.63,39.16,88.69,25,95.27"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09ae3" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 20.50 20.50)">1</text>
	<text transform="matrix(1 0 0 1 28.50 10.50)">2</text>
	<text transform="matrix(1 0 0 1 31.50 20.50)">3</text>
	<text transform="matrix(1 0 0 1 31.54 27.50)">4</text>
	<text transform="matrix(1 0 0 1 4.50 40.50)">5</text>
	<text transform="matrix(1 0 0 1 17.50 47.50)">6</text>
	<text transform="matrix(1 0 0 1 48.50 43.50)">7</text>
	<text transform="matrix(1 0 0 1 72.50 8.50)">8</text>
	<text transform="matrix(1 0 0 1 76.50 23.50)">9</text>
	<text transform="matrix(1 0 0 1 78.50 35.50)">10</text>
	<text transform="matrix(1 0 0 1 43.75 58.50)">11</text>
	<text transform="matrix(1 0 0 1 5.50 65.50)">12</text>
	<text transform="matrix(1 0 0 1 58.50 68.50)">13</text>
	<text transform="matrix(1 0 0 1 38.50 72.50)">14</text>
</g>
</svg>
