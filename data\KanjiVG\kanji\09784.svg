<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09784" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09784" kvg:element="鞄">
	<g id="kvg:09784-g1" kvg:element="革" kvg:position="left" kvg:radical="general">
		<g id="kvg:09784-g2" kvg:element="廿" kvg:position="top">
			<g id="kvg:09784-g3" kvg:element="十">
				<path id="kvg:09784-s1" kvg:type="㇐" d="M12.4,27.88c0.62,0.27,1.77,0.31,2.4,0.27c3.27-0.24,25.84-3.71,32.72-3.83c1.04-0.02,1.67,0.13,2.19,0.26"/>
				<path id="kvg:09784-s2" kvg:type="㇑" d="M21.35,17.96c0.47,0.33,0.75,1.48,0.84,2.14C23,26,23.92,32.04,24.42,39.5"/>
			</g>
			<path id="kvg:09784-s3" kvg:type="㇑a" d="M39.06,15.25c0.46,0.33,0.9,1.47,0.84,2.14c-0.65,6.86-0.83,9.42-2.07,18.63"/>
			<path id="kvg:09784-s4" kvg:type="㇐b" d="M26.33,37.25c1.77,0,11.68-1.23,13.27-1.37"/>
		</g>
		<g id="kvg:09784-g4" kvg:position="bottom">
			<path id="kvg:09784-s5" kvg:type="㇑" d="M16.26,47.6c0.29,0.47,0.58,0.86,0.7,1.45c0.99,4.66,1.83,6.53,2.5,14.91"/>
			<path id="kvg:09784-s6" kvg:type="㇕" d="M17.48,48.71c8.4-1.71,23.42-3.03,27.02-3.69c1.32-0.24,2.43,1.45,2.2,2.85c-0.47,2.88-1.75,5.38-2.78,10.4"/>
			<path id="kvg:09784-s7" kvg:type="㇐" d="M19.25,61.88c4.23-0.75,19-2.38,25.98-2.88"/>
			<path id="kvg:09784-s8" kvg:type="㇐" d="M12.75,74.2c0.86,0.54,2.43,0.67,3.29,0.54c6.96-0.99,21.23-3.49,30.95-3.73c1.43-0.04,2.29,0.26,3.01,0.53"/>
			<path id="kvg:09784-s9" kvg:type="㇑" d="M30.46,39.25C31,40,31.5,41.48,31.5,42.5c0,8.5,0,47.75-0.12,54"/>
		</g>
	</g>
	<g id="kvg:09784-g5" kvg:element="包" kvg:position="right">
		<g id="kvg:09784-g6" kvg:element="勹">
			<g id="kvg:09784-g7" kvg:element="丿">
				<path id="kvg:09784-s10" kvg:type="㇒" d="M66.31,16c0.04,0.61,0.22,1.63-0.08,2.46c-1.96,5.41-7.29,15.58-14.62,23.49"/>
			</g>
			<path id="kvg:09784-s11" kvg:type="㇆" d="M61.59,32.03c0.9,0.59,2.12,0.57,3.61,0.4c3.55-0.42,19.06-3.14,22.66-3.48c1.71-0.16,3.64,1.08,3.56,3.3c-0.24,6.66-2.66,26.02-8.62,37.58c-1.92,3.72-4.14,1.98-6.27-0.49"/>
		</g>
		<g id="kvg:09784-g8" kvg:element="己">
			<path id="kvg:09784-s12" kvg:type="㇕c" d="M54.56,46.09c1.18,0.91,2.18,0.85,3.91,0.61c5.02-0.7,13.36-2.16,14.43-2.28c1.42-0.15,2.53,1,2.02,3.35c-0.15,0.7-2.1,5.8-3.8,12.07"/>
			<path id="kvg:09784-s13" kvg:type="㇐" d="M57.05,62.61c3.8-0.43,11.42-1.73,16.82-2.11"/>
			<path id="kvg:09784-s14" kvg:type="㇟" d="M55.52,47c0.75,1.06,0.4,2.31,0.39,3.95c-0.03,5.25,0.13,25.2,0.13,29.79c0,9.96,0.2,12.7,19.25,12.7c17.72,0,18.86-2.94,18.86-13.97"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09784" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 5.50 28.85)">1</text>
	<text transform="matrix(1 0 0 1 12.50 17.50)">2</text>
	<text transform="matrix(1 0 0 1 30.50 13.50)">3</text>
	<text transform="matrix(1 0 0 1 27.50 34.50)">4</text>
	<text transform="matrix(1 0 0 1 9.50 55.50)">5</text>
	<text transform="matrix(1 0 0 1 17.50 45.85)">6</text>
	<text transform="matrix(1 0 0 1 22.50 58.50)">7</text>
	<text transform="matrix(1 0 0 1 4.50 75.50)">8</text>
	<text transform="matrix(1 0 0 1 35.50 44.50)">9</text>
	<text transform="matrix(1 0 0 1 55.50 16.50)">10</text>
	<text transform="matrix(1 0 0 1 67.50 29.50)">11</text>
	<text transform="matrix(1 0 0 1 57.50 44.50)">12</text>
	<text transform="matrix(1 0 0 1 58.50 59.50)">13</text>
	<text transform="matrix(1 0 0 1 48.50 54.50)">14</text>
</g>
</svg>
