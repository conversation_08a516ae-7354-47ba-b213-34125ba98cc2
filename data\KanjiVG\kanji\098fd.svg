<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_098fd" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:098fd" kvg:element="飽">
	<g id="kvg:098fd-g1" kvg:element="飠" kvg:original="食" kvg:partial="true" kvg:position="left" kvg:radical="general">
		<path id="kvg:098fd-s1" kvg:type="㇒" d="M32.54,14.25c0.06,0.71-0.23,1.65-0.61,2.59C28.5,25.12,20.62,35.75,12,43.25"/>
		<path id="kvg:098fd-s2" kvg:type="㇔/㇏" d="M34,19.75c4.56,2.3,9.92,6.64,12.5,11.5"/>
		<path id="kvg:098fd-s3" kvg:type="㇑a" d="M33.04,31.51c0.97,0.97,1.28,2.24,1.28,3.57c0,0.93-0.07,5.62-0.07,7.92"/>
		<path id="kvg:098fd-s4" kvg:type="㇕" d="M21.2,44.76c1.05,0.74,3.13,0.96,4.55,0.72c4.44-0.73,11.01-1.76,14.41-2.31c3.97-0.64,5.4-0.06,4.8,4.21c-0.65,4.64-1.31,10.48-1.9,15.12c-0.36,2.82-0.85,4.82-0.85,5.02"/>
		<path id="kvg:098fd-s5" kvg:type="㇐a" d="M24.46,56.53c6.54-1.16,12.79-2.41,18.53-2.91"/>
		<path id="kvg:098fd-s6" kvg:type="㇐a" d="M24.37,68.85c5.13-0.98,12.01-1.85,17.21-2.47"/>
		<path id="kvg:098fd-s7" kvg:type="㇙" d="M22.12,45.51c0.81,0.81,1.03,1.99,1.03,3.24c0,0.8,0.07,30.27,0.08,40.5c0,3.4,0.51,4,3.34,2.09C30.6,88.61,38.09,84.01,41.5,82"/>
		<path id="kvg:098fd-s8" kvg:type="㇔/㇏" d="M38.75,75.25c2.39,2.38,6.14,8.72,7.5,13.75"/>
	</g>
	<g id="kvg:098fd-g2" kvg:element="包" kvg:position="right" kvg:phon="包">
		<g id="kvg:098fd-g3" kvg:element="勹">
			<g id="kvg:098fd-g4" kvg:element="丿">
				<path id="kvg:098fd-s9" kvg:type="㇒" d="M65.57,14.5c0.04,0.67,0.21,1.78-0.08,2.69c-1.86,5.94-6.93,16.86-13.89,25.53"/>
			</g>
			<path id="kvg:098fd-s10" kvg:type="㇆" d="M63.25,29.93c1.98,0.5,4,0.56,6.02,0.16c5.11-1.02,14.56-2.86,19.21-3.6c3.73-0.59,5.66,0.59,5.41,4.64c-0.69,11.04-2.66,22.37-5.96,33.36c-1.51,5.02-4.59,6.56-8.22,2.06"/>
		</g>
		<g id="kvg:098fd-g5" kvg:element="己">
			<path id="kvg:098fd-s11" kvg:type="㇕c" d="M56.64,46.24c1.14,0.94,2.86,0.94,4.54,0.63c2.69-0.5,9.27-2.25,12.32-3.03c2.44-0.62,4.16,0.11,3.56,3.1c-0.15,0.73-1.28,5.3-2.93,11.78"/>
			<path id="kvg:098fd-s12" kvg:type="㇐" d="M55.05,61.1c0.73,0.17,1.76,0.82,2.97,0.69c4.99-0.54,9.26-0.91,13.72-1.67c1.97-0.33,3.72-0.36,5.03-0.46"/>
			<path id="kvg:098fd-s13" kvg:type="㇟" d="M56.33,62.02c1.01,1.01,1.09,1.85,1.09,3.34c0,5.43-0.05,11.53-0.05,16.27c0,10.3,1.38,12.06,18.98,12.06c17.64,0,19.54-1.44,19.54-12.89"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_098fd" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 23.25 19.63)">1</text>
	<text transform="matrix(1 0 0 1 42.75 19.63)">2</text>
	<text transform="matrix(1 0 0 1 26.50 36.50)">3</text>
	<text transform="matrix(1 0 0 1 24.75 43.50)">4</text>
	<text transform="matrix(1 0 0 1 27.50 53.50)">5</text>
	<text transform="matrix(1 0 0 1 27.75 65.83)">6</text>
	<text transform="matrix(1 0 0 1 15.75 54.13)">7</text>
	<text transform="matrix(1 0 0 1 32.25 79.63)">8</text>
	<text transform="matrix(1 0 0 1 57.50 16.50)">9</text>
	<text transform="matrix(1 0 0 1 69.50 26.50)">10</text>
	<text transform="matrix(1 0 0 1 59.50 43.55)">11</text>
	<text transform="matrix(1 0 0 1 57.75 58.48)">12</text>
	<text transform="matrix(1 0 0 1 47.25 69.50)">13</text>
</g>
</svg>
