<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_099e2" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:099e2" kvg:element="駢">
	<g id="kvg:099e2-g1" kvg:element="馬" kvg:position="left" kvg:radical="general">
		<path id="kvg:099e2-s1" kvg:type="㇑" d="M15.24,18.43c0.76,0.82,1,2.68,0.99,3.57c-0.05,9.13-0.27,38.09-0.98,40.87"/>
		<path id="kvg:099e2-s2" kvg:type="㇐b" d="M17.77,19.57c7.82-0.53,20.4-2.37,22.76-2.73c0.94-0.14,2.53-0.25,3-0.03"/>
		<path id="kvg:099e2-s3" kvg:type="㇑a" d="M28.81,19.63c0.25,0.38,0.96,0.9,0.95,1.51c-0.02,6.29-0.06,28.5-0.25,38.39"/>
		<path id="kvg:099e2-s4" kvg:type="㇐b" d="M16.66,34.16c6.26-0.6,19.75-2.63,22.44-3.03c0.94-0.14,2.53-0.25,3-0.02"/>
		<path id="kvg:099e2-s5" kvg:type="㇐b" d="M16.76,46.95c6.26-0.6,20.25-2.64,22.94-3.04c0.94-0.14,2.53-0.25,3-0.03"/>
		<path id="kvg:099e2-s6" kvg:type="㇆a" d="M15.99,62.26c6.17-1.44,20.04-3.61,23.43-4.22c4.33-0.78,4.19,2.86,3.95,6.58c-0.67,10.55-2.92,21.25-6.12,28.09c-2.75,5.86-5.32,0.42-6.21-0.88"/>
		<g id="kvg:099e2-g2" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:099e2-s7" kvg:type="㇔" d="M11,71.62c0.28,6.89-0.58,12.96-0.88,14.34"/>
			<path id="kvg:099e2-s8" kvg:type="㇔" d="M17.61,70.71c1.27,2.56,2.35,5.78,2.64,10.35"/>
			<path id="kvg:099e2-s9" kvg:type="㇔" d="M24.27,69.03c0.78,1.26,3.58,5.42,3.93,9.54"/>
			<path id="kvg:099e2-s10" kvg:type="㇔" d="M31.22,67.47c1.4,2.23,3.69,4.53,4.2,7.49"/>
		</g>
	</g>
	<g id="kvg:099e2-g3" kvg:element="并" kvg:position="right">
		<g id="kvg:099e2-g4" kvg:position="top">
			<path id="kvg:099e2-s11" kvg:type="㇔" d="M54.05,12.59c3.5,2.95,9.04,12.12,9.91,16.71"/>
			<path id="kvg:099e2-s12" kvg:type="㇒" d="M84.11,10.39c0.02,0.35,0.04,0.91-0.04,1.42C83.56,14.8,80,23.5,76.11,29.36"/>
		</g>
		<g id="kvg:099e2-g5" kvg:position="bottom">
			<g id="kvg:099e2-g6" kvg:element="干" kvg:part="1">
				<path id="kvg:099e2-s13" kvg:type="㇐" d="M50.89,34.49c0.92,0.5,2.61,0.61,3.53,0.5c6.81-0.79,26.72-3.68,35.46-3.68c1.53,0,2.45,0.24,3.22,0.49"/>
				<g id="kvg:099e2-g7" kvg:element="十" kvg:part="1">
					<path id="kvg:099e2-s14" kvg:type="㇐" d="M48.75,57.53c1.25,0.46,3.53,0.55,4.78,0.46c13.48-1.04,26.47-3.83,41.11-4.36c2.08-0.08,3.32,0.22,4.36,0.44"/>
				</g>
			</g>
			<path id="kvg:099e2-s15" kvg:type="㇒" d="M62.21,36.93c0.79,1.32,1.42,2.07,1.42,4.21c0,19.6,1.56,46.94-18.84,58.94"/>
			<g id="kvg:099e2-g8" kvg:element="干" kvg:part="2">
				<g id="kvg:099e2-g9" kvg:element="十" kvg:part="2">
					<path id="kvg:099e2-s16" kvg:type="㇑" d="M79.75,34.73c0.61,0.42,0.98,1.91,1.11,2.76c0.12,0.85,0,56.69-0.12,62"/>
				</g>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_099e2" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 9.50 27.50)">1</text>
	<text transform="matrix(1 0 0 1 16.66 16.85)">2</text>
	<text transform="matrix(1 0 0 1 33.50 26.50)">3</text>
	<text transform="matrix(1 0 0 1 20.32 30.50)">4</text>
	<text transform="matrix(1 0 0 1 20.50 43.50)">5</text>
	<text transform="matrix(1 0 0 1 20.50 57.50)">6</text>
	<text transform="matrix(1 0 0 1 5.50 74.50)">7</text>
	<text transform="matrix(1 0 0 1 13.60 78.50)">8</text>
	<text transform="matrix(1 0 0 1 20.50 75.50)">9</text>
	<text transform="matrix(1 0 0 1 25.50 71.50)">10</text>
	<text transform="matrix(1 0 0 1 47.50 10.50)">11</text>
	<text transform="matrix(1 0 0 1 72.50 8.50)">12</text>
	<text transform="matrix(1 0 0 1 48.50 31.50)">13</text>
	<text transform="matrix(1 0 0 1 46.50 54.50)">14</text>
	<text transform="matrix(1 0 0 1 53.50 43.50)">15</text>
	<text transform="matrix(1 0 0 1 69.50 41.50)">16</text>
</g>
</svg>
