<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09921" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09921" kvg:element="餡">
	<g id="kvg:09921-g1" kvg:element="⻞" kvg:variant="true" kvg:original="食" kvg:position="left" kvg:radical="general">
		<path id="kvg:09921-s1" kvg:type="㇒" d="M31.25,10.64c0.06,0.67,0.3,1.8-0.12,2.71c-2.74,5.95-10.63,17.6-21.82,26.37"/>
		<path id="kvg:09921-s2" kvg:type="㇔/㇏" d="M33.52,16.33c4.69,1.83,12.12,7.53,13.29,10.38"/>
		<path id="kvg:09921-s3" kvg:type="㇐" d="M22.75,31.16c0.39,0.15,1.11,0.19,1.5,0.15c2.5-0.25,10.7-2.17,13.12-2c0.65,0.05,1.05,0.07,1.37,0.14"/>
		<path id="kvg:09921-s4" kvg:type="㇑" d="M18.77,39.47c0.4,0.82,0.8,1.75,0.8,2.85c0,1.09-0.13,55.83,0,56.93"/>
		<path id="kvg:09921-s5" kvg:type="㇕" d="M19.72,41.18c2.26-0.14,18.49-2.27,20.56-2.44c1.72-0.14,2.83,1.52,2.69,2.32c-0.27,1.64-2.06,18.86-2.62,22.5"/>
		<path id="kvg:09921-s6" kvg:type="㇐a" d="M20.12,51.81c3.07,0,17.92-2.41,21.39-2.41"/>
		<path id="kvg:09921-s7" kvg:type="㇐a" d="M19.98,63.68c6.24-0.59,12.68-1.66,20.46-2.16"/>
		<path id="kvg:09921-s8" kvg:type="㇐c" d="M19.92,76.76c3.84-0.27,16.45-2.29,20.17-2.11c1,0.05,1.61,0.07,2.11,0.15"/>
		<path id="kvg:09921-s9" kvg:type="㇐c" d="M20.44,90.63c3.84-0.27,16.96-2.27,20.68-2.09c1,0.05,1.61,0.07,2.11,0.15"/>
	</g>
	<g id="kvg:09921-g2" kvg:element="臽" kvg:position="right">
		<g id="kvg:09921-g3" kvg:element="𠂊" kvg:variant="true" kvg:original="勹" kvg:position="top">
			<path id="kvg:09921-s10" kvg:type="㇒" d="M66.24,14.5c0.05,0.81,0.1,2.08-0.1,3.24c-1.2,6.83-8.1,21.8-17.56,30.97"/>
			<path id="kvg:09921-s11" kvg:type="㇇" d="M67.29,24.85c0.65,0.27,1.55,0.32,2.6,0.18c4.01-0.52,15.11-3.2,17.71-3.29c2.59-0.09,3.11,0.91,2.73,2.82c-0.39,1.91-4.33,15.69-8.59,22.2c-1.27,1.94-3.73,1.99-5.68-1.31"/>
		</g>
		<g id="kvg:09921-g4" kvg:element="臼" kvg:position="bottom">
			<path id="kvg:09921-s12" kvg:type="㇒" d="M69.19,51.09c0.04,0.25,0.17,0.74-0.08,1.02c-3.37,3.63-7.3,6.86-14.97,9.75"/>
			<path id="kvg:09921-s13" kvg:type="㇑" d="M52.48,60.91c0.65,0.69,0.91,2.22,1.07,3.63c0.58,5.15,2.94,25.26,3.53,32.21"/>
			<path id="kvg:09921-s14" kvg:type="㇐" d="M55.06,77.18c3.21-0.31,12.47-2.49,13.24-2.49c0.76,0,1.83,0,2.44,0"/>
			<path id="kvg:09921-s15" kvg:type="㇕" d="M77.21,58.92c0.76,0.16,1.38,0.47,2.44,0.47c1.07,0,12.37-1.88,13.59-2.03c1.22-0.16,2.23,0.78,2.14,1.72c-0.92,9.08-4.06,35.33-4.21,35.96"/>
			<path id="kvg:09921-s16" kvg:type="㇐" d="M77.01,74.1c0.76,0.16,1.37,0.47,2.44,0.47c1.07,0,12.88-1.25,14.11-1.41"/>
			<path id="kvg:09921-s17" kvg:type="㇐" d="M57.24,94.09c1.99,0,31.03-2.04,33.33-2.19"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09921" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 23.50 10.50)">1</text>
	<text transform="matrix(1 0 0 1 37.50 15.50)">2</text>
	<text transform="matrix(1 0 0 1 28.50 27.50)">3</text>
	<text transform="matrix(1 0 0 1 12.50 47.50)">4</text>
	<text transform="matrix(1 0 0 1 21.50 38.50)">5</text>
	<text transform="matrix(1 0 0 1 23.50 48.25)">6</text>
	<text transform="matrix(1 0 0 1 23.54 60.50)">7</text>
	<text transform="matrix(1 0 0 1 23.51 73.42)">8</text>
	<text transform="matrix(1 0 0 1 23.51 87.50)">9</text>
	<text transform="matrix(1 0 0 1 54.50 14.50)">10</text>
	<text transform="matrix(1 0 0 1 71.25 21.50)">11</text>
	<text transform="matrix(1 0 0 1 57.50 51.50)">12</text>
	<text transform="matrix(1 0 0 1 43.50 68.50)">13</text>
	<text transform="matrix(1 0 0 1 58.50 73.50)">14</text>
	<text transform="matrix(1 0 0 1 76.50 57.50)">15</text>
	<text transform="matrix(1 0 0 1 74.50 70.50)">16</text>
	<text transform="matrix(1 0 0 1 59.50 90.50)">17</text>
</g>
</svg>
