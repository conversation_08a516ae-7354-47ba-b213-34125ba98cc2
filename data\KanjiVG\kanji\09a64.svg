<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09a64" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09a64" kvg:element="驤">
	<g id="kvg:09a64-g1" kvg:element="馬" kvg:position="left" kvg:radical="general">
		<path id="kvg:09a64-s1" kvg:type="㇑" d="M14.74,17.19c0.51,0.56,0.99,2.93,0.99,4.06c-0.05,9.65-0.28,37.77-0.98,40.52"/>
		<path id="kvg:09a64-s2" kvg:type="㇐b" d="M17.27,18.83c7.82-0.54,20.4-2.88,22.76-3.23c0.94-0.14,2.53-0.25,3-0.03"/>
		<path id="kvg:09a64-s3" kvg:type="㇑a" d="M28.31,18.39c0.25,0.39,0.96,0.9,0.95,1.52c-0.02,6.31-0.06,28.59-0.25,38.51"/>
		<path id="kvg:09a64-s4" kvg:type="㇐b" d="M16.16,33.46c6.26-0.6,19.75-3.13,22.44-3.54c0.94-0.14,2.53-0.25,3-0.02"/>
		<path id="kvg:09a64-s5" kvg:type="㇐b" d="M15.76,45.79c6.26-0.6,20.25-2.15,22.94-2.55c0.94-0.14,2.53-0.25,3-0.03"/>
		<path id="kvg:09a64-s6" kvg:type="㇆a" d="M15.49,61.15c6.17-1.44,20.04-3.63,23.43-4.24c4.33-0.78,4.19,2.87,3.95,6.6c-0.67,10.58-2.42,21.32-5.62,28.18c-2.75,5.88-5.32,0.42-6.21-0.88"/>
		<g id="kvg:09a64-g2" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09a64-s7" kvg:type="㇔" d="M10.29,71.32c0.28,6.55-0.57,12.31-0.87,13.62"/>
			<path id="kvg:09a64-s8" kvg:type="㇔" d="M16.72,70.29c1.35,2.52,2.5,5.7,2.81,10.19"/>
			<path id="kvg:09a64-s9" kvg:type="㇔" d="M23.59,68.42c0.7,1.18,2.91,5.08,4.01,8.95"/>
			<path id="kvg:09a64-s10" kvg:type="㇔" d="M30.28,66.63c1.56,2.28,4.13,4.63,4.69,7.65"/>
		</g>
	</g>
	<g id="kvg:09a64-g3" kvg:element="襄" kvg:position="right">
		<g id="kvg:09a64-g4" kvg:element="衣" kvg:part="1">
			<g id="kvg:09a64-g5" kvg:element="亠" kvg:position="top">
				<path id="kvg:09a64-s11" kvg:type="㇑a" d="M68.05,11c0.58,0.39,1.54,1.74,1.54,2.64c0,1.48-0.08,4.76-0.08,6.38"/>
				<path id="kvg:09a64-s12" kvg:type="㇐" d="M48.78,22.36c0.98,0.11,3.19,0.18,4.15,0.11c7.07-0.48,24.69-2.97,35.1-3.3c1.61-0.05,2.43,0.05,3.63,0.78"/>
			</g>
		</g>
		<g id="kvg:09a64-g6" kvg:element="口" kvg:position="left">
			<path id="kvg:09a64-s13" kvg:type="㇑" d="M50.6,29.87c0.23,0.21,0.5,0.38,0.57,0.66c0.77,2.79,1.39,6.93,1.95,10.73"/>
			<path id="kvg:09a64-s14" kvg:type="㇕b" d="M52.05,31.3c4.77-1.1,10.12-2.29,12.85-2.57c1-0.1,1.6,0.6,1.46,1.2c-0.59,2.47-0.81,4.4-1.69,7.81"/>
			<path id="kvg:09a64-s15" kvg:type="㇐b" d="M53.23,39.9c3.21-0.31,8.02-1.37,12.43-1.85"/>
		</g>
		<g id="kvg:09a64-g7" kvg:element="口" kvg:position="right">
			<path id="kvg:09a64-s16" kvg:type="㇑" d="M72.5,28.64c0.25,0.21,0.51,0.39,0.62,0.66c0.87,2.11,1.49,5.92,2.08,9.7"/>
			<path id="kvg:09a64-s17" kvg:type="㇕b" d="M73.56,30.07c5.11-1.1,12.48-2.31,15.41-2.59c1.07-0.1,1.71,0.6,1.56,1.19c-0.63,2.46-1.79,4.38-2.73,7.79"/>
			<path id="kvg:09a64-s18" kvg:type="㇐b" d="M75.32,37.65c3.44-0.31,8.65-0.9,13.38-1.39"/>
		</g>
		<g id="kvg:09a64-g8" kvg:element="三" kvg:part="1">
			<g id="kvg:09a64-g9" kvg:element="一" kvg:position="top">
				<path id="kvg:09a64-s19" kvg:type="㇐" d="M52.2,49.69c0.73,0.27,2.83,0.32,3.56,0.27c6.64-0.51,24.77-2.47,31.91-3.06c1.21-0.1,1.94,0.13,2.55,0.26"/>
			</g>
		</g>
		<path id="kvg:09a64-s20" kvg:type="㇑" d="M61.02,44.44c1.11,0.46,1.62,2.5,1.62,3.46c0,3.1,0.3,12.1,0.3,19.51"/>
		<path id="kvg:09a64-s21" kvg:type="㇑" d="M76.25,43.25c0.9,0.91,0.83,2,0.73,3.42c-0.39,5.58-0.58,12.33-1.12,19.46"/>
		<g id="kvg:09a64-g10" kvg:element="三" kvg:part="1" kvg:position="bottom">
			<g id="kvg:09a64-g11" kvg:element="一">
				<path id="kvg:09a64-s22" kvg:type="㇐" d="M52.82,58.55c0.84,0.36,3.27,0.44,4.12,0.36c7.68-0.7,21.13-2.02,29.38-2.83c1.4-0.14,2.24,0.17,2.95,0.35"/>
			</g>
			<g id="kvg:09a64-g12" kvg:element="一">
				<path id="kvg:09a64-s23" kvg:type="㇐" d="M47.98,69.17c1.23,0.35,3.48,0.49,4.71,0.35C63.93,68.25,81,66.25,92.2,65.57c2.04-0.12,3.28,0.17,4.3,0.34"/>
			</g>
		</g>
		<g id="kvg:09a64-g13" kvg:element="衣" kvg:part="2" kvg:position="bottom">
			<path id="kvg:09a64-s24" kvg:type="㇒" d="M64.58,68.89c0.05,0.42,0.2,1.11-0.1,1.68c-1.96,3.65-8.34,11.19-18.17,16.95"/>
			<path id="kvg:09a64-s25" kvg:type="㇙" d="M59.69,81.15c0.36,0.29,0.76,1.58,0.76,2.08c-0.02,6.4-0.75,10.93-0.75,12.02c0,1.08,0.9,1.7,2.04,0.89s8.6-7.39,11.04-9.23"/>
			<path id="kvg:09a64-s26" kvg:type="㇒" d="M88.28,71.7c0.03,0.21-0.31,1.42-0.48,1.7c-1.06,1.7-3.77,3.71-9.18,7.1"/>
			<path id="kvg:09a64-s27" kvg:type="㇏" d="M67.83,70.91c0.96,0.57,2.59,1.87,3.5,3.08C76.25,80.5,84.5,88.5,91.76,92.91c1.39,0.84,2.35,1.45,3.61,1.74"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09a64" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 9.50 27.50)">1</text>
	<text transform="matrix(1 0 0 1 17.50 15.50)">2</text>
	<text transform="matrix(1 0 0 1 32.50 24.85)">3</text>
	<text transform="matrix(1 0 0 1 19.34 29.50)">4</text>
	<text transform="matrix(1 0 0 1 19.50 42.50)">5</text>
	<text transform="matrix(1 0 0 1 19.38 57.50)">6</text>
	<text transform="matrix(1 0 0 1 5.50 72.85)">7</text>
	<text transform="matrix(1 0 0 1 12.50 75.50)">8</text>
	<text transform="matrix(1 0 0 1 19.50 72.50)">9</text>
	<text transform="matrix(1 0 0 1 23.50 69.50)">10</text>
	<text transform="matrix(1 0 0 1 57.50 10.50)">11</text>
	<text transform="matrix(1 0 0 1 46.50 19.50)">12</text>
	<text transform="matrix(1 0 0 1 43.25 37.73)">13</text>
	<text transform="matrix(1 0 0 1 51.50 29.50)">14</text>
	<text transform="matrix(1 0 0 1 54.50 36.50)">15</text>
	<text transform="matrix(1 0 0 1 67.50 36.50)">16</text>
	<text transform="matrix(1 0 0 1 74.50 27.50)">17</text>
	<text transform="matrix(1 0 0 1 77.50 35.50)">18</text>
	<text transform="matrix(1 0 0 1 43.25 49.50)">19</text>
	<text transform="matrix(1 0 0 1 51.25 46.50)">20</text>
	<text transform="matrix(1 0 0 1 67.50 45.50)">21</text>
	<text transform="matrix(1 0 0 1 45.50 57.50)">22</text>
	<text transform="matrix(1 0 0 1 46.50 66.50)">23</text>
	<text transform="matrix(1 0 0 1 50.50 77.13)">24</text>
	<text transform="matrix(1 0 0 1 50.50 92.50)">25</text>
	<text transform="matrix(1 0 0 1 78.50 74.50)">26</text>
	<text transform="matrix(1 0 0 1 63.75 80.50)">27</text>
</g>
</svg>
