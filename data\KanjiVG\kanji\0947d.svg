<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0947d" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0947d" kvg:element="鑽">
	<g id="kvg:0947d-g1" kvg:element="金" kvg:position="left" kvg:radical="general">
		<path id="kvg:0947d-s1" kvg:type="㇒" d="M27.25,14.24c0,0.78,0.05,1.4-0.14,2.33c-1,4.87-10.5,21.1-18.63,28.3"/>
		<path id="kvg:0947d-s2" kvg:type="㇔/㇏" d="M28.89,19.71c3.75,1.66,8.54,6.09,10.36,10.29"/>
		<path id="kvg:0947d-s3" kvg:type="㇐" d="M16.5,40.64c1.68,0,2.44,0.12,2.98,0.07c4.84-0.41,10.42-2.01,16.12-2.3c0.82-0.04,1-0.07,2.4-0.07"/>
		<path id="kvg:0947d-s4" kvg:type="㇐" d="M13.09,54.81c0.59,0.33,2.56,0.48,3.17,0.43C21.75,54.75,30,53,37.04,52.08c0.74-0.1,2.38-0.13,3.49,0.15"/>
		<path id="kvg:0947d-s5" kvg:type="㇑a" d="M26.45,40.69c1.24,0.78,1.24,2.52,1.24,3.14c0,4.35-0.19,35.67-0.19,41.98"/>
		<path id="kvg:0947d-s6" kvg:type="㇔" d="M13.89,64.01c4.08,5.88,6.07,12.25,6.9,15.69"/>
		<path id="kvg:0947d-s7" kvg:type="㇒" d="M40.2,59.55c0.3,0.84,0.46,2.06,0.34,2.76c-0.3,1.81-2.21,5.63-5.62,10.97"/>
		<path id="kvg:0947d-s8" kvg:type="㇀/㇐" d="M12,90.78c1.28,0.96,2.82,0.72,4.62,0.24c1.33-0.35,11.54-5.51,24.88-11.27"/>
	</g>
	<g id="kvg:0947d-g2" kvg:element="贊" kvg:position="right">
		<g id="kvg:0947d-g3" kvg:position="top">
			<g id="kvg:0947d-g4" kvg:element="先" kvg:position="left">
				<g id="kvg:0947d-g5" kvg:position="top">
					<path id="kvg:0947d-s9" kvg:type="㇒" d="M50.28,15.88c0.02,0.28,0.06,0.73-0.04,1.12c-0.66,2.34-2.23,7-3.99,9.73"/>
					<path id="kvg:0947d-s10" kvg:type="㇐" d="M49.47,21.59c0.31,0.15,1.79,0.19,2.1,0.15c2.93-0.31,9.25-1.28,12.57-1.76c0.52-0.07,1.73,0.07,1.99,0.15"/>
					<path id="kvg:0947d-s11" kvg:type="㇑a" d="M56.46,12.29c0.28,0.2,0.89,1.53,0.94,1.94s0.03,13.04-0.03,15.6"/>
					<path id="kvg:0947d-s12" kvg:type="㇐" d="M44.89,32.19c0.7,0.25,2,0.34,2.7,0.25c5.89-0.77,10.37-1.75,17.86-2.58c1.17-0.13,1.88,0.12,2.46,0.25"/>
				</g>
				<g id="kvg:0947d-g6" kvg:element="儿" kvg:original="八" kvg:position="bottom">
					<g id="kvg:0947d-g7" kvg:element="丿">
						<path id="kvg:0947d-s13" kvg:type="㇒" d="M53.62,34.18c0.02,0.29,0.04,0.74-0.04,1.15c-0.5,2.43-3.04,7.77-6.93,11.03"/>
					</g>
					<path id="kvg:0947d-s14" kvg:type="㇟" d="M59.28,30.87c0.21,0.61,0.37,1.33,0.38,2.27c0.03,2.45-0.08,5.65-0.08,8.55c0,1.61,0.81,1.57,1.7,0.95C64,40.75,66,39,68.71,37.9"/>
				</g>
			</g>
			<g id="kvg:0947d-g8" kvg:element="先" kvg:position="right">
				<g id="kvg:0947d-g9" kvg:position="top">
					<path id="kvg:0947d-s15" kvg:type="㇒" d="M75.13,12.69c0.03,0.27,0.09,0.7-0.05,1.07c-1.05,2.75-2.97,6.67-4.66,9.34"/>
					<path id="kvg:0947d-s16" kvg:type="㇐" d="M75.4,19.1c0.35,0.17,2.03,0.2,2.38,0.17c3.32-0.34,10.51-2.38,14.27-2.9c0.59-0.08,1.97,0.08,2.26,0.16"/>
					<path id="kvg:0947d-s17" kvg:type="㇑a" d="M81.42,10.55c0.26,0.19,0.8,1.39,0.85,1.76c0.05,0.37,0.03,11.8-0.03,14.12"/>
					<path id="kvg:0947d-s18" kvg:type="㇐" d="M71.71,28.71c0.7,0.31,1.97,0.34,2.66,0.31c5.55-0.25,15.55-2.38,19.81-2.94c1.15-0.15,1.85,0.15,2.43,0.3"/>
				</g>
				<g id="kvg:0947d-g10" kvg:element="儿" kvg:original="八" kvg:position="bottom">
					<g id="kvg:0947d-g11" kvg:element="丿">
						<path id="kvg:0947d-s19" kvg:type="㇒" d="M78.99,30.82c0.02,0.27,0.04,0.7-0.04,1.09c-0.48,2.29-2.93,7.33-6.68,10.41"/>
					</g>
					<path id="kvg:0947d-s20" kvg:type="㇟" d="M84.83,28.2c0.23,0.4,0.42,0.87,0.43,1.49c0.04,1.61-0.18,3.92-0.18,5.82c0,3.34,0.14,4.36,5.38,4.37c8.01,0.01,6.65-1.9,6.73-3.07"/>
				</g>
			</g>
		</g>
		<g id="kvg:0947d-g12" kvg:element="貝" kvg:position="bottom">
			<g id="kvg:0947d-g13" kvg:element="目" kvg:position="top">
				<path id="kvg:0947d-s21" kvg:type="㇑" d="M54.6,47.15c0.49,1.22,1.21,2.13,1.21,3.76c0,1.63,0.23,25.73-0.36,32.56"/>
				<path id="kvg:0947d-s22" kvg:type="㇕a" d="M56.63,49.02c2.79-0.2,24.57-2.31,27.11-2.55c2.12-0.2,3.25,1.12,3.25,2.65c0,3.72-0.62,31.87-0.62,33.09"/>
				<path id="kvg:0947d-s23" kvg:type="㇐a" d="M55.86,59.58c5.22-0.47,25.97-2.33,30.44-2.33"/>
				<path id="kvg:0947d-s24" kvg:type="㇐a" d="M56.09,70.26c7.88-0.9,21.89-2.22,30.25-2.4"/>
				<path id="kvg:0947d-s25" kvg:type="㇐a" d="M55.73,81.73c7.88-0.67,23.42-1.94,30.61-1.94"/>
			</g>
			<g id="kvg:0947d-g14" kvg:element="八" kvg:position="bottom">
				<path id="kvg:0947d-s26" kvg:type="㇒" d="M62.82,85.55c0.42,1.05-0.32,2.07-1.16,2.84c-2.54,2.3-9.2,7.22-14.28,9.94"/>
				<path id="kvg:0947d-s27" kvg:type="㇔" d="M81.1,86.36c5.93,3.34,12.95,9.4,14.57,12.33"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0947d" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 19.50 13.50)">1</text>
	<text transform="matrix(1 0 0 1 32.50 18.50)">2</text>
	<text transform="matrix(1 0 0 1 21.50 37.50)">3</text>
	<text transform="matrix(1 0 0 1 5.50 55.50)">4</text>
	<text transform="matrix(1 0 0 1 20.50 49.50)">5</text>
	<text transform="matrix(1 0 0 1 7.50 65.50)">6</text>
	<text transform="matrix(1 0 0 1 32.50 61.50)">7</text>
	<text transform="matrix(1 0 0 1 4.50 91.50)">8</text>
	<text transform="matrix(1 0 0 1 42.50 17.50)">9</text>
	<text transform="matrix(1 0 0 1 51.50 19.50)">10</text>
	<text transform="matrix(1 0 0 1 49.50 9.38)">11</text>
	<text transform="matrix(1 0 0 1 47.50 30.50)">12</text>
	<text transform="matrix(1 0 0 1 44.25 39.50)">13</text>
	<text transform="matrix(1 0 0 1 60.50 37.50)">14</text>
	<text transform="matrix(1 0 0 1 66.50 8.83)">15</text>
	<text transform="matrix(1 0 0 1 75.75 16.50)">16</text>
	<text transform="matrix(1 0 0 1 76.00 6.43)">17</text>
	<text transform="matrix(1 0 0 1 72.25 26.50)">18</text>
	<text transform="matrix(1 0 0 1 69.25 35.78)">19</text>
	<text transform="matrix(1 0 0 1 87.25 34.45)">20</text>
	<text transform="matrix(1 0 0 1 47.00 56.38)">21</text>
	<text transform="matrix(1 0 0 1 62.50 46.50)">22</text>
	<text transform="matrix(1 0 0 1 59.49 57.50)">23</text>
	<text transform="matrix(1 0 0 1 59.34 67.13)">24</text>
	<text transform="matrix(1 0 0 1 59.50 79.50)">25</text>
	<text transform="matrix(1 0 0 1 49.50 90.50)">26</text>
	<text transform="matrix(1 0 0 1 72.50 92.50)">27</text>
</g>
</svg>
