<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09760" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09760" kvg:element="靠">
	<g id="kvg:09760-g1" kvg:element="告" kvg:variant="true" kvg:position="top">
		<g id="kvg:09760-g2" kvg:element="牛" kvg:position="top">
			<path id="kvg:09760-s1" kvg:type="㇒" d="M35.32,11.26c0.03,0.37,0.07,0.95-0.06,1.47c-0.77,3.11-5.17,9.93-11.2,14.11"/>
			<path id="kvg:09760-s2" kvg:type="㇐" d="M31.89,19.13c1.02,0.34,4.34,0.41,5.36,0.34c12.46-0.92,30.26-2.17,41.71-2.46c1.69-0.04,2.71,0.16,3.55,0.33"/>
			<path id="kvg:09760-s3" kvg:type="㇐" d="M15.33,30.83c1.57,0.47,4.44,0.55,6.01,0.47c16.32-0.8,42.25-2.73,66.84-3.58c2.61-0.09,4.18,0.23,5.49,0.47"/>
			<path id="kvg:09760-s4" kvg:type="㇑" d="M52.72,8.38c0.68,0.24,1.75,1.12,1.75,2.8c0,0.5-0.09,19.21-0.22,22.24"/>
		</g>
		<g id="kvg:09760-g3" kvg:element="口" kvg:position="bottom">
			<path id="kvg:09760-s5" kvg:type="㇑" d="M31.34,38.08c0.35,0.22,0.7,0.41,0.85,0.69c1.2,2.2,2.24,8.49,3.06,12.45"/>
			<path id="kvg:09760-s6" kvg:type="㇕b" d="M32.81,38.84c9.29-0.51,40.86-2.7,45.24-3.01c1.6-0.11,2.56,1.15,2.33,1.82c-0.95,2.77-1.43,4.85-3.46,8.85"/>
			<path id="kvg:09760-s7" kvg:type="㇐b" d="M35.94,49.49c8.04-0.3,38.15-2.62,43.92-2.89"/>
		</g>
	</g>
	<g id="kvg:09760-g4" kvg:element="非" kvg:position="bottom" kvg:radical="general">
		<g id="kvg:09760-g5" kvg:position="left">
			<path id="kvg:09760-s8" kvg:type="㇓" d="M45.43,53.83c0.82,0.67,1.32,2.21,1.61,2.75c0.12,0.23,0.14,5.69,0.1,14.44C47.08,82.77,46.27,92.72,28.28,100"/>
			<path id="kvg:09760-s9" kvg:type="㇐" d="M16.57,63.06c1.43,0.44,3.42,0.62,4.99,0.5c1.58-0.12,23.42-1.81,25.26-2.16"/>
			<path id="kvg:09760-s10" kvg:type="㇐" d="M16.91,74.12c2.09,0.38,3.42,0.62,4.99,0.5c1.58-0.12,20.64-1.55,24.84-1.9"/>
			<path id="kvg:09760-s11" kvg:type="㇐" d="M15.2,88.43c1.12,1.13,2.66,1.82,4.52,1.12c1.1-0.41,18.67-6.25,23.32-8.23"/>
		</g>
		<g id="kvg:09760-g6" kvg:position="right">
			<path id="kvg:09760-s12" kvg:type="㇑" d="M61.16,53.75c1.09,1,1.34,2.05,1.5,2.51s0,41.23-0.17,44.14"/>
			<path id="kvg:09760-s13" kvg:type="㇐" d="M62.91,61.14c5.64-0.22,23.08-1.84,27.31-2.21c1.54-0.13,0.47-0.07,3.13-0.07"/>
			<path id="kvg:09760-s14" kvg:type="㇐" d="M63.38,71.32c5.64-0.22,23.71-1.55,27.94-1.92c1.54-0.14,0.47-0.07,3.13-0.07"/>
			<path id="kvg:09760-s15" kvg:type="㇐" d="M63.22,83.14c5.64-0.22,25.22-1.05,29.45-1.42c1.54-0.13,0.47-0.07,3.13-0.07"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09760" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 29.25 10.63)">1</text>
	<text transform="matrix(1 0 0 1 40.25 16.50)">2</text>
	<text transform="matrix(1 0 0 1 8.75 31.50)">3</text>
	<text transform="matrix(1 0 0 1 44.50 7.50)">4</text>
	<text transform="matrix(1 0 0 1 25.50 45.50)">5</text>
	<text transform="matrix(1 0 0 1 35.50 37.50)">6</text>
	<text transform="matrix(1 0 0 1 39.50 47.50)">7</text>
	<text transform="matrix(1 0 0 1 37.50 57.50)">8</text>
	<text transform="matrix(1 0 0 1 9.50 63.50)">9</text>
	<text transform="matrix(1 0 0 1 6.50 75.50)">10</text>
	<text transform="matrix(1 0 0 1 5.50 89.50)">11</text>
	<text transform="matrix(1 0 0 1 51.25 56.50)">12</text>
	<text transform="matrix(1 0 0 1 66.49 57.50)">13</text>
	<text transform="matrix(1 0 0 1 66.32 68.65)">14</text>
	<text transform="matrix(1 0 0 1 66.50 79.50)">15</text>
</g>
</svg>
