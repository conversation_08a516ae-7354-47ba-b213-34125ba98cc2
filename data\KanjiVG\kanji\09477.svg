<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09477" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09477" kvg:element="鑷">
	<g id="kvg:09477-g1" kvg:element="金" kvg:position="left" kvg:radical="general">
		<path id="kvg:09477-s1" kvg:type="㇒" d="M27.25,13.24c0,0.74,0.05,1.33-0.13,2.22c-0.93,4.63-9.8,20.07-17.39,26.91"/>
		<path id="kvg:09477-s2" kvg:type="㇔/㇏" d="M29.39,18.46c4.47,2.03,10.18,7.42,12.36,12.54"/>
		<path id="kvg:09477-s3" kvg:type="㇐" d="M16.5,41c1.73,0,2.52,0.09,3.08,0.06c5.01-0.31,10.79-1.53,16.69-1.75c0.85-0.03,1.04-0.05,2.48-0.05"/>
		<path id="kvg:09477-s4" kvg:type="㇐" d="M11.59,54.81c0.59,0.33,2.56,0.48,3.17,0.43c5.49-0.49,14.99-2.24,21.78-3.17c0.74-0.1,2.38-0.13,3.49,0.15"/>
		<path id="kvg:09477-s5" kvg:type="㇑a" d="M26.45,42.19c1.24,0.78,1.24,2.52,1.24,3.14c0,4.35,0.12,35.63-0.19,39.98"/>
		<path id="kvg:09477-s6" kvg:type="㇔" d="M13.14,65.26c3.63,5.04,5.41,10.5,6.15,13.44"/>
		<path id="kvg:09477-s7" kvg:type="㇒" d="M39.7,59.55c0.3,0.84,0.46,2.06,0.34,2.76c-0.3,1.81-2.71,5.13-6.12,10.47"/>
		<path id="kvg:09477-s8" kvg:type="㇀/㇐" d="M12,90.31c1.22,0.92,2.68,0.69,4.38,0.23C17.64,90.2,27.34,85.26,40,79.75"/>
	</g>
	<g id="kvg:09477-g2" kvg:element="聶" kvg:position="right">
		<g id="kvg:09477-g3" kvg:element="耳" kvg:position="top">
			<path id="kvg:09477-s9" kvg:type="㇐" d="M50.08,13.91c1.08,0.26,3.05,0.31,4.13,0.26c8.55-0.41,26.05-1.91,35.67-2.44c1.79-0.1,2.87,0.12,3.77,0.25"/>
			<path id="kvg:09477-s10" kvg:type="㇑a" d="M57.32,15.56c0.76,0.76,1.17,1.26,1.17,3.32c0,2.06-0.23,15.02-0.23,22.42"/>
			<path id="kvg:09477-s11" kvg:type="㇐a" d="M59.68,23.19c3.45,0,16.67-1.39,22.58-1.39"/>
			<path id="kvg:09477-s12" kvg:type="㇐a" d="M58.75,31.65c6.76-0.65,15.85-1.61,24.03-2.21"/>
			<path id="kvg:09477-s13" kvg:type="㇀" d="M46.09,42.56c0.35,0.5,1.24,1.21,2.47,1c4.93-0.84,33.83-5.86,41.41-7.2"/>
			<path id="kvg:09477-s14" kvg:type="㇑" d="M81.9,12.99c0.35,1.02,0.89,2.29,0.89,3.57c0,1.27,0.18,20.06,0.18,30.51"/>
		</g>
		<g id="kvg:09477-g4" kvg:position="bottom">
			<g id="kvg:09477-g5" kvg:element="耳" kvg:position="left">
				<path id="kvg:09477-s15" kvg:type="㇐" d="M45.16,54.99c0.44,0.29,1.24,0.36,1.68,0.29c3.91-0.64,10.76-1.97,19.58-2.83c0.73-0.07,1.17,0.14,1.54,0.28"/>
				<path id="kvg:09477-s16" kvg:type="㇑a" d="M50.76,56.29c0.38,0.43,0.58,1.57,0.58,2.73s-0.12,21.56-0.12,25.76"/>
				<path id="kvg:09477-s17" kvg:type="㇐a" d="M51.61,64.29c2.49-0.4,8.17-1.86,12-2.22"/>
				<path id="kvg:09477-s18" kvg:type="㇐a" d="M51.43,73.4c2.14,0,9.12-2.08,12.24-2.08"/>
				<path id="kvg:09477-s19" kvg:type="㇀" d="M44.75,86.91c0.5,0.63,1.2,0.97,1.72,0.72c2.1-1,13.76-6.54,16.99-8.15"/>
				<path id="kvg:09477-s20" kvg:type="㇑" d="M63.43,53.98c0.18,0.58,0.45,1.3,0.45,2.02c0,0.72,0.09,37.61,0.09,43.54"/>
			</g>
			<g id="kvg:09477-g6" kvg:element="耳" kvg:position="right">
				<path id="kvg:09477-s21" kvg:type="㇐" d="M72.92,52.88c0.54,0.3,1.53,0.33,2.08,0.3c4.55-0.27,17.75-1.93,21.85-2.44c0.9-0.11,1.45,0.14,1.9,0.29"/>
				<path id="kvg:09477-s22" kvg:type="㇑a" d="M77.87,55.24c0.44,0.45,0.68,1.63,0.68,2.83s-0.14,20.84-0.14,25.19"/>
				<path id="kvg:09477-s23" kvg:type="㇐a" d="M78.74,62.04c2,0,9.68-1.8,13.11-1.8"/>
				<path id="kvg:09477-s24" kvg:type="㇐a" d="M78.54,71.49c2.48,0,9.4-1.65,13.01-1.65"/>
				<path id="kvg:09477-s25" kvg:type="㇀" d="M71.83,86.18c0.18,0.62,1.63,0.99,2.26,0.74c2.51-1.03,19.22-9.72,23.07-11.37"/>
				<path id="kvg:09477-s26" kvg:type="㇑" d="M91.65,51.34c0.21,0.6,0.52,1.35,0.52,2.1c0,0.75,0.1,38.91,0.1,45.06"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09477" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 20.85 14.50)">1</text>
	<text transform="matrix(1 0 0 1 33.50 18.50)">2</text>
	<text transform="matrix(1 0 0 1 20.50 38.50)">3</text>
	<text transform="matrix(1 0 0 1 4.50 55.50)">4</text>
	<text transform="matrix(1 0 0 1 20.50 50.50)">5</text>
	<text transform="matrix(1 0 0 1 7.01 64.75)">6</text>
	<text transform="matrix(1 0 0 1 33.38 62.63)">7</text>
	<text transform="matrix(1 0 0 1 10.78 88.10)">8</text>
	<text transform="matrix(1 0 0 1 47.50 10.50)">9</text>
	<text transform="matrix(1 0 0 1 46.37 22.62)">10</text>
	<text transform="matrix(1 0 0 1 59.99 21.25)">11</text>
	<text transform="matrix(1 0 0 1 59.99 29.25)">12</text>
	<text transform="matrix(1 0 0 1 46.00 39.48)">13</text>
	<text transform="matrix(1 0 0 1 84.50 21.50)">14</text>
	<text transform="matrix(1 0 0 1 44.75 52.83)">15</text>
	<text transform="matrix(1 0 0 1 39.99 62.75)">16</text>
	<text transform="matrix(1 0 0 1 52.12 61.37)">17</text>
	<text transform="matrix(1 0 0 1 52.12 70.37)">18</text>
	<text transform="matrix(1 0 0 1 39.87 85.51)">19</text>
	<text transform="matrix(1 0 0 1 64.75 60.25)">20</text>
	<text transform="matrix(1 0 0 1 70.49 51.13)">21</text>
	<text transform="matrix(1 0 0 1 68.11 68.01)">22</text>
	<text transform="matrix(1 0 0 1 79.62 59.50)">23</text>
	<text transform="matrix(1 0 0 1 79.37 69.50)">24</text>
	<text transform="matrix(1 0 0 1 67.24 84.50)">25</text>
	<text transform="matrix(1 0 0 1 94.50 60.13)">26</text>
</g>
</svg>
