<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09781" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09781" kvg:element="鞁">
	<g id="kvg:09781-g1" kvg:element="革" kvg:position="left" kvg:radical="general">
		<g id="kvg:09781-g2" kvg:element="廿" kvg:position="top">
			<g id="kvg:09781-g3" kvg:element="十">
				<path id="kvg:09781-s1" kvg:type="㇐" d="M8.15,25.14c0.93,0.33,2.65,0.25,3.59,0.33C18.25,26,36,23,45.94,21.98c1.55-0.16,2.5,0.16,3.28,0.32"/>
				<path id="kvg:09781-s2" kvg:type="㇑" d="M19.37,14.96c0.49,0.4,0.79,1.8,0.89,2.6c0.86,7.16,1.37,12.89,1.9,21.94"/>
			</g>
			<path id="kvg:09781-s3" kvg:type="㇑a" d="M37.55,12.75c0.52,0.37,1.01,1.65,0.94,2.4c-0.73,7.68-0.36,10.56-1.75,20.88"/>
			<path id="kvg:09781-s4" kvg:type="㇐b" d="M22.33,37.25c1.77,0,14.68-1.23,16.27-1.37"/>
		</g>
		<g id="kvg:09781-g4" kvg:position="bottom">
			<path id="kvg:09781-s5" kvg:type="㇑" d="M14.01,48.55c0.3,0.51,0.61,0.93,0.74,1.56c1.04,5.01,2.42,7.59,3.13,16.6"/>
			<path id="kvg:09781-s6" kvg:type="㇕" d="M15.29,49.73c8.81-1.83,25.07-3.25,28.85-3.96c1.38-0.26,2.55,1.56,2.31,3.07c-0.5,3.1-1.83,5.36-2.92,10.75"/>
			<path id="kvg:09781-s7" kvg:type="㇐" d="M17.65,64.47c4.44-0.8,19.93-3.56,27.26-4.09"/>
			<path id="kvg:09781-s8" kvg:type="㇐" d="M9.75,75.7c1.06,0.54,2.99,0.57,4.06,0.54C23,76,33.78,74,45.79,72.52c1.75-0.21,2.82,0.26,3.71,0.53"/>
			<path id="kvg:09781-s9" kvg:type="㇑" d="M29.96,38.75c0.36,1,0.54,2.23,0.54,3.25c0,8.5,0,50.75-0.12,57"/>
		</g>
	</g>
	<g id="kvg:09781-g5" kvg:element="皮" kvg:position="right">
		<path id="kvg:09781-s10" kvg:type="㇒" d="M54.63,29.37c0.67,1.22,1.67,4.54,1.65,7.07c-0.13,28.58-1.78,44.06-13.41,56.7"/>
		<path id="kvg:09781-s11" kvg:type="㇖b" d="M56.13,31.51c12.4-2.76,28.38-5.34,33.51-5.68c9.03-0.59,1.15,7.91-0.13,8.78"/>
		<path id="kvg:09781-s12" kvg:type="㇑a" d="M70.47,12.25c1.5,0.5,2.39,2.25,2.69,3.25s-0.11,24-0.11,34.25"/>
		<path id="kvg:09781-s13" kvg:type="㇇" d="M58.65,51.1c1.31,0.37,1.6,0.98,4.21,0.43c5.58-1.17,17.9-3.18,23.46-4.04c1.85-0.29,3.26,2.13,2.45,4.03C82.25,66.75,71.5,87,53.62,97.39"/>
		<path id="kvg:09781-s14" kvg:type="㇏" d="M61.54,57.37C64.5,59.5,77.25,83,89.48,93.04c1.75,1.43,2.79,2.62,4.3,3.15"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09781" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 2.50 24.50)">1</text>
	<text transform="matrix(1 0 0 1 10.50 13.50)">2</text>
	<text transform="matrix(1 0 0 1 28.50 11.50)">3</text>
	<text transform="matrix(1 0 0 1 25.50 34.50)">4</text>
	<text transform="matrix(1 0 0 1 7.50 55.50)">5</text>
	<text transform="matrix(1 0 0 1 16.58 46.50)">6</text>
	<text transform="matrix(1 0 0 1 20.50 60.50)">7</text>
	<text transform="matrix(1 0 0 1 1.60 76.50)">8</text>
	<text transform="matrix(1 0 0 1 33.50 44.85)">9</text>
	<text transform="matrix(1 0 0 1 45.50 36.50)">10</text>
	<text transform="matrix(1 0 0 1 57.50 27.50)">11</text>
	<text transform="matrix(1 0 0 1 58.50 12.50)">12</text>
	<text transform="matrix(1 0 0 1 59.75 48.50)">13</text>
	<text transform="matrix(1 0 0 1 57.50 69.50)">14</text>
</g>
</svg>
