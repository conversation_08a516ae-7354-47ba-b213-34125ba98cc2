<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09aea" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09aea" kvg:element="髪">
	<g id="kvg:09aea-g1" kvg:element="髟" kvg:position="top" kvg:radical="general">
		<g id="kvg:09aea-g2" kvg:element="長" kvg:variant="true" kvg:position="left">
			<path id="kvg:09aea-s1" kvg:type="㇑a" d="M27.25,16.31c0.97,0.97,1.14,2.06,1.14,3.46c0,1.02-0.02,19.11-0.02,23.48"/>
			<path id="kvg:09aea-s2" kvg:type="㇐b" d="M28.94,17.83c6.08-0.77,12.75-1.65,18.95-2.58c1.59-0.24,2.93-0.5,4.51-0.21"/>
			<path id="kvg:09aea-s3" kvg:type="㇐b" d="M29.52,27.14c3.77-0.32,11.06-1.59,15.01-2.12c1.53-0.2,3.29-0.61,4.83-0.28"/>
			<path id="kvg:09aea-s4" kvg:type="㇐b" d="M29.78,35.63c3.61-0.3,10.63-1.31,14.72-1.81c1.67-0.21,3.68-0.7,5.36-0.34"/>
			<path id="kvg:09aea-s5" kvg:type="㇐" d="M14.38,45.02c1.83,0.48,3.91,0.18,5.76,0.03c7.81-0.64,20.09-2.55,29.85-3.15c2.16-0.13,4.33-0.3,6.48,0.04"/>
			<g id="kvg:09aea-g3" kvg:element="厶">
				<path id="kvg:09aea-s6" kvg:type="㇜" d="M30.98,46.37c0.12,0.8-0.46,1.61-0.94,2.17c-2.69,3.1-4.41,4.7-7.18,7.47c-1.04,1.04-0.78,2.57,1.11,2.09c7.29-1.85,14.29-3.6,24.16-6.35"/>
				<path id="kvg:09aea-s7" kvg:type="㇔" d="M44.62,47.25c2.54,1.21,6.57,4.96,7.2,6.83"/>
			</g>
		</g>
		<g id="kvg:09aea-g4" kvg:element="彡" kvg:position="right">
			<path id="kvg:09aea-s8" kvg:type="㇒" d="M83.24,13c-0.37,0.88-0.74,1.45-1.47,2.04c-3.17,2.54-8.84,5.45-19.21,8.53"/>
			<path id="kvg:09aea-s9" kvg:type="㇒" d="M86.36,26.03c-0.11,0.84-0.78,1.65-1.38,2.1c-4.03,2.99-12.29,7.74-24.65,11.23"/>
			<path id="kvg:09aea-s10" kvg:type="㇒" d="M89.83,41.29c-0.21,0.83-0.86,1.69-1.45,2.02C83.62,46,76.75,49.75,61.43,53.5"/>
		</g>
	</g>
	<g id="kvg:09aea-g5" kvg:element="友" kvg:position="bottom" kvg:phon="友">
		<path id="kvg:09aea-s11" kvg:type="㇐" d="M21.74,66.24c2.53,0.9,5.53,0.67,8.13,0.32c14.64-1.95,33.36-4.89,51.13-5.37c2.74-0.07,5.44-0.14,8.14,0.44"/>
		<path id="kvg:09aea-s12" kvg:type="㇒" d="M51.28,58.55c0.08,0.82,0.04,1.94-0.42,3.06C47.5,69.75,37.5,86,21,93.5"/>
		<g id="kvg:09aea-g6" kvg:element="又">
			<path id="kvg:09aea-s13" kvg:type="㇇" d="M47.68,73.46c1.49,0.11,2.9,0,4.35-0.33c4.63-1.05,10.71-2.46,14.96-3.4c2.68-0.59,3.13,1.15,2.04,2.96c-3.92,6.5-18.91,21.07-36.99,25.82"/>
			<path id="kvg:09aea-s14" kvg:type="㇏" d="M43.28,78.16c3.85,1.34,23.42,12.34,33.05,16.8c3.05,1.42,6.52,2.99,9.88,3.41"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09aea" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 21.50 25.50)">1</text>
	<text transform="matrix(1 0 0 1 27.75 14.50)">2</text>
	<text transform="matrix(1 0 0 1 32.25 24.50)">3</text>
	<text transform="matrix(1 0 0 1 32.25 33.50)">4</text>
	<text transform="matrix(1 0 0 1 8.25 43.63)">5</text>
	<text transform="matrix(1 0 0 1 18.50 54.50)">6</text>
	<text transform="matrix(1 0 0 1 38.25 51.50)">7</text>
	<text transform="matrix(1 0 0 1 74.50 12.13)">8</text>
	<text transform="matrix(1 0 0 1 77.50 27.05)">9</text>
	<text transform="matrix(1 0 0 1 78.75 41.98)">10</text>
	<text transform="matrix(1 0 0 1 12.50 68.50)">11</text>
	<text transform="matrix(1 0 0 1 41.50 60.50)">12</text>
	<text transform="matrix(1 0 0 1 52.50 70.50)">13</text>
	<text transform="matrix(1 0 0 1 50.50 80.50)">14</text>
</g>
</svg>
