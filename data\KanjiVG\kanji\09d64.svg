<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09d64" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09d64" kvg:element="鵤">
	<g id="kvg:09d64-g1" kvg:element="角" kvg:position="left">
		<g id="kvg:09d64-g2" kvg:element="𠂊" kvg:variant="true" kvg:original="勹" kvg:position="top">
			<path id="kvg:09d64-s1" kvg:type="㇒" d="M27.22,10c0.05,0.56,0.1,1.44-0.09,2.25c-1.1,4.74-7.45,15.14-16.14,21.5"/>
			<path id="kvg:09d64-s2" kvg:type="㇇a" d="M24.39,19c2,0,12.11-1.75,15.82-2.75c2.16-0.58,3.29,1.14,2,2.75c-4.21,5.25-5.49,10.25-8.65,16"/>
		</g>
		<g id="kvg:09d64-g3" kvg:position="bottom">
			<path id="kvg:09d64-s3" kvg:type="㇒" d="M18.42,36c0.74,0.76,1.47,2.89,1.48,3.69c0.02,1.95-0.53,24.23-0.67,25.94c-0.92,10.97-3,21.07-9.22,27.88"/>
			<path id="kvg:09d64-s4" kvg:type="㇆a" d="M21.07,37.7c2.5-0.21,20.92-3.73,22.78-3.96c3.33-0.42,4.58,2.71,4.16,3.96c-0.4,1.2-0.62,31.55-0.62,48.43c0,12.12-4.39,3.37-6.68,1.62"/>
			<path id="kvg:09d64-s5" kvg:type="㇑a" d="M32.42,37.37c0.05,0.24,0.68,1.25,0.73,2.77C33.38,47.42,33,64.52,33,67"/>
			<path id="kvg:09d64-s6" kvg:type="㇐a" d="M20.07,52.85C30.5,51.5,37.5,50.75,47.7,49.5"/>
			<path id="kvg:09d64-s7" kvg:type="㇐a" d="M20.07,68.75c6.68-0.5,17.43-2.25,27.19-3"/>
		</g>
	</g>
	<g id="kvg:09d64-g4" kvg:element="鳥" kvg:position="right" kvg:radical="general">
		<path id="kvg:09d64-s8" kvg:type="㇒" d="M74.04,9.91c0.02,0.31,0.04,0.8-0.04,1.24c-0.46,2.58-3.08,8.11-6.67,11.33"/>
		<path id="kvg:09d64-s9" kvg:type="㇑" d="M60.8,23.92c0.33,0.41,0.6,0.95,0.6,1.62c0,6.77,0.04,32.78-0.17,43.42"/>
		<path id="kvg:09d64-s10" kvg:type="㇕a" d="M61.81,25.16c1.55,0,20-2.96,21.51-2.86c2.24,0.15,3.33,2.01,3.13,3.76c-0.12,1.07-1.64,11.85-3.08,20.06"/>
		<path id="kvg:09d64-s11" kvg:type="㇐a" d="M62.06,35.71c1.9,0.12,20.6-2.57,22.75-2.43"/>
		<path id="kvg:09d64-s12" kvg:type="㇐a" d="M61.97,46.51c3.88-0.13,16.55-2.33,21.38-2.34"/>
		<path id="kvg:09d64-s13" kvg:type="㇐b" d="M61.69,56.9c8.79-0.99,27.74-2.84,31.51-3.46c1.32-0.22,3.55-0.41,4.21-0.12"/>
		<path id="kvg:09d64-s14" kvg:type="㇆a" d="M61.24,69.17c9.1-1.53,26.13-3.2,31.17-3.65c3.28-0.29,4.97,0.83,4.38,4.28c-1.64,9.58-4.08,18.68-6.93,24.57c-2.86,5.92-5.97,0.98-7.3-0.23"/>
		<g id="kvg:09d64-g5" kvg:element="灬" kvg:variant="true" kvg:original="火">
			<path id="kvg:09d64-s15" kvg:type="㇔" d="M55.93,79.85c0.42,4.01-0.3,8.82-1.55,11.27"/>
			<path id="kvg:09d64-s16" kvg:type="㇔" d="M63.07,78.97c1.5,1.97,2.92,7.13,3.29,10.02"/>
			<path id="kvg:09d64-s17" kvg:type="㇔" d="M71.47,76.13c1.59,1.6,4.11,6.41,4.51,8.77"/>
			<path id="kvg:09d64-s18" kvg:type="㇔" d="M79.91,74.21c1.8,1.67,4.66,6.71,5.11,9.17"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09d64" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 19.50 9.50)">1</text>
	<text transform="matrix(1 0 0 1 31.50 14.50)">2</text>
	<text transform="matrix(1 0 0 1 11.50 43.50)">3</text>
	<text transform="matrix(1 0 0 1 20.50 34.50)">4</text>
	<text transform="matrix(1 0 0 1 35.50 44.33)">5</text>
	<text transform="matrix(1 0 0 1 23.50 49.50)">6</text>
	<text transform="matrix(1 0 0 1 23.50 65.50)">7</text>
	<text transform="matrix(1 0 0 1 64.50 10.50)">8</text>
	<text transform="matrix(1 0 0 1 54.50 31.50)">9</text>
	<text transform="matrix(1 0 0 1 76.75 20.50)">10</text>
	<text transform="matrix(1 0 0 1 65.50 32.50)">11</text>
	<text transform="matrix(1 0 0 1 65.31 43.50)">12</text>
	<text transform="matrix(1 0 0 1 65.36 53.50)">13</text>
	<text transform="matrix(1 0 0 1 65.19 66.65)">14</text>
	<text transform="matrix(1 0 0 1 49.50 78.08)">15</text>
	<text transform="matrix(1 0 0 1 57.50 88.50)">16</text>
	<text transform="matrix(1 0 0 1 67.50 86.50)">17</text>
	<text transform="matrix(1 0 0 1 75.50 83.50)">18</text>
</g>
</svg>
