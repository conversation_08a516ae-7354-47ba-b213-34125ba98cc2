<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0947e" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0947e" kvg:element="鑾">
	<g id="kvg:0947e-g1" kvg:element="䜌" kvg:position="top">
		<g id="kvg:0947e-g2" kvg:element="言">
			<path id="kvg:0947e-s1" kvg:type="㇔" d="M46.58,10.5c3.29,1.16,8.5,4.75,9.32,6.55"/>
			<path id="kvg:0947e-s2" kvg:type="㇐" d="M41.06,22.09c0.53,0.16,2.53,0.18,3.06,0.16c6.51-0.29,12.67-1.32,17.4-1.46c0.88-0.03,2.2,0.08,2.64,0.16"/>
			<path id="kvg:0947e-s3" kvg:type="㇐" d="M44.95,28.95c0.32,0.08,2.27,0.09,2.59,0.08c3.31-0.09,7.47-1.03,10.95-1.4c0.53-0.06,2.75,0.04,3.01,0.08"/>
			<path id="kvg:0947e-s4" kvg:type="㇐" d="M44.78,34.82c0.28,0.08,2.02,0.09,2.31,0.08c2.96-0.09,8.78-0.94,11.7-0.93c0.47,0,2.45,0.04,2.69,0.08"/>
			<g id="kvg:0947e-g3" kvg:element="口">
				<path id="kvg:0947e-s5" kvg:type="㇑" d="M44.45,41.37c0.29,0.25,0.62,0.45,0.72,0.77c0.83,2.87,0.88,4.39,1.72,8.61"/>
				<path id="kvg:0947e-s6" kvg:type="㇕b" d="M46.27,42.55c5.38-0.61,9.5-1.59,14.46-2.21c1.24-0.15,1.96,0.7,1.82,1.41c-0.3,1.5-1.05,4.25-1.7,6.06"/>
				<path id="kvg:0947e-s7" kvg:type="㇐b" d="M47.49,49.25c4.01-0.37,9.1-0.5,14.6-1.07"/>
			</g>
		</g>
		<g id="kvg:0947e-g4" kvg:element="糸">
			<path id="kvg:0947e-s8" kvg:type="㇜" d="M25.4,10.25c0.31,0.99,0.61,1.85-0.16,2.82c-1.74,2.18-3.99,5.18-6.32,7.43c-0.57,0.55-0.62,2.3,0,2.49c3.26,0.99,3.84,1.65,6.64,3.48"/>
			<path id="kvg:0947e-s9" kvg:type="㇜" d="M34.07,15.6c0.25,0.4,0.33,1.7,0,2.13c-4.87,6.24-10.81,11.35-17.5,17.04c-1.53,1.3,0.38,2.05,1.38,1.79c4.18-1.11,12.87-2.89,18.25-3.95"/>
			<path id="kvg:0947e-s10" kvg:type="㇔" d="M33,28.43c2.12,1.43,5.47,5.86,6,8.07"/>
			<path id="kvg:0947e-s11" kvg:type="㇑" d="M25.83,38.05c0.05,0.23,0.73,1.21,0.78,2.68c0.25,7.08-0.16,14.6-0.16,17.01"/>
			<path id="kvg:0947e-s12" kvg:type="㇒" d="M19.01,42.37c0.1,0.58,0.08,1.71-0.1,2.12c-1.2,2.69-4.21,7.87-5.91,10.18"/>
			<path id="kvg:0947e-s13" kvg:type="㇔" d="M33,41.83c2.75,2.19,4.86,7.27,5.5,9.26"/>
		</g>
		<g id="kvg:0947e-g5" kvg:element="糸">
			<path id="kvg:0947e-s14" kvg:type="㇜" d="M77.87,8.25c0.26,0.89,0.35,1.55-0.13,2.52c-0.99,1.98-3.49,5.48-4.93,7.55c-0.39,0.56-0.52,2.05,0,2.22c2.74,0.89,3.75,0.87,6.1,2.5"/>
			<path id="kvg:0947e-s15" kvg:type="㇜" d="M86.14,12.22c0.24,0.4,0.31,1.72,0,2.15c-4.65,6.3-9.01,11.75-15.4,17.5c-1.46,1.32,0.36,2.07,1.32,1.8c4-1.12,14.11-3.03,19.25-4.1"/>
			<path id="kvg:0947e-s16" kvg:type="㇔" d="M87.62,24.75c2.45,1.64,6.31,6.73,6.92,9.27"/>
			<path id="kvg:0947e-s17" kvg:type="㇑" d="M79.64,34.83c0.04,0.21,0.61,1.08,0.66,2.4c0.21,6.32-0.13,16.87-0.13,19.02"/>
			<path id="kvg:0947e-s18" kvg:type="㇒" d="M73.29,40.1c0.11,0.5,0.1,1.48-0.11,1.83c-1.37,2.32-4.79,6.8-6.72,8.8"/>
			<path id="kvg:0947e-s19" kvg:type="㇔" d="M87.84,40.53c3.15,2.55,5.56,8.49,6.29,10.8"/>
		</g>
	</g>
	<g id="kvg:0947e-g6" kvg:element="金" kvg:position="bottom" kvg:radical="general">
		<g id="kvg:0947e-g7" kvg:position="top">
			<path id="kvg:0947e-s20" kvg:type="㇒" d="M54,52.5c0,0.58,0.11,1.03-0.32,1.73C51.46,57.83,32.09,71.21,14,76.53"/>
			<path id="kvg:0947e-s21" kvg:type="㇏" d="M54.25,55.3c8.5,2.8,31.24,15.42,34,16.3c2.82,0.9,3.74,1.06,5,1.15"/>
		</g>
		<g id="kvg:0947e-g8" kvg:position="bottom">
			<path id="kvg:0947e-s22" kvg:type="㇐" d="M36.02,71.16c0.73,0.25,3.12,0.28,3.87,0.26c4.36-0.17,17.11-1.17,30.53-2.29c1.12-0.09,2.16,0.05,3.1,0.26"/>
			<path id="kvg:0947e-s23" kvg:type="㇐" d="M26.8,80.58c1.07,0.47,4.6,0.67,5.7,0.62c8.25-0.44,34-2.69,45.94-3.46c1.34-0.09,4.28-0.19,6.27,0.22"/>
			<path id="kvg:0947e-s24" kvg:type="㇑" d="M52.97,72.96c0.89,0.69,0.89,2.23,0.89,2.78c0,3.84,0.44,17.35,0.22,21.19"/>
			<path id="kvg:0947e-s25" kvg:type="㇔" d="M34.25,85.67c4.28,3.51,6.38,7.31,7.25,9.36"/>
			<path id="kvg:0947e-s26" kvg:type="㇒" d="M73.58,81.28c0.35,0.8,0.23,2.04-0.11,2.64c-1.47,2.58-5.05,7.55-7.97,10"/>
			<path id="kvg:0947e-s27" kvg:type="㇐" d="M21,98.29c0.73,0.23,4.39,0.84,6,0.72c13.25-1,39.5-2.75,58.82-3.32c2.19-0.06,4.83,0.29,5.93,0.56"/>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0947e" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 41.50 10.50)">1</text>
	<text transform="matrix(1 0 0 1 39.50 19.50)">2</text>
	<text transform="matrix(1 0 0 1 39.50 28.50)">3</text>
	<text transform="matrix(1 0 0 1 39.50 34.50)">4</text>
	<text transform="matrix(1 0 0 1 40.50 46.50)">5</text>
	<text transform="matrix(1 0 0 1 45.50 41.50)">6</text>
	<text transform="matrix(1 0 0 1 50.50 48.50)">7</text>
	<text transform="matrix(1 0 0 1 19.50 9.50)">8</text>
	<text transform="matrix(1 0 0 1 30.50 12.50)">9</text>
	<text transform="matrix(1 0 0 1 31.50 27.50)">10</text>
	<text transform="matrix(1 0 0 1 18.50 52.50)">11</text>
	<text transform="matrix(1 0 0 1 7.50 44.50)">12</text>
	<text transform="matrix(1 0 0 1 28.50 50.50)">13</text>
	<text transform="matrix(1 0 0 1 67.50 8.50)">14</text>
	<text transform="matrix(1 0 0 1 83.50 9.50)">15</text>
	<text transform="matrix(1 0 0 1 88.50 24.50)">16</text>
	<text transform="matrix(1 0 0 1 72.50 50.50)">17</text>
	<text transform="matrix(1 0 0 1 64.50 41.50)">18</text>
	<text transform="matrix(1 0 0 1 89.50 42.50)">19</text>
	<text transform="matrix(1 0 0 1 39.50 58.50)">20</text>
	<text transform="matrix(1 0 0 1 66.50 58.50)">21</text>
	<text transform="matrix(1 0 0 1 42.75 69.50)">22</text>
	<text transform="matrix(1 0 0 1 24.50 79.13)">23</text>
	<text transform="matrix(1 0 0 1 43.75 78.13)">24</text>
	<text transform="matrix(1 0 0 1 25.50 91.50)">25</text>
	<text transform="matrix(1 0 0 1 61.50 87.50)">26</text>
	<text transform="matrix(1 0 0 1 11.50 98.50)">27</text>
</g>
</svg>
