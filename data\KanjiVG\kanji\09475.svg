<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_09475" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:09475" kvg:element="鑵">
	<g id="kvg:09475-g1" kvg:element="金" kvg:position="left" kvg:radical="general">
		<path id="kvg:09475-s1" kvg:type="㇒" d="M30.25,13.74c0,0.8,0.05,1.43-0.16,2.39C29,21.12,18.61,37.75,9.72,45.13"/>
		<path id="kvg:09475-s2" kvg:type="㇔/㇏" d="M31.89,19.96C36,21.75,41.25,26.5,43.25,31"/>
		<path id="kvg:09475-s3" kvg:type="㇐" d="M19,41.5c1.5,0,2.18,0.09,2.67,0.06C26,41.24,33.5,39.03,38.6,38.8c0.74-0.03,0.9-0.05,2.15-0.05"/>
		<path id="kvg:09475-s4" kvg:type="㇐" d="M13.59,55.81c0.59,0.33,2.56,0.48,3.17,0.43c5.49-0.49,15.99-1.99,23.28-3.17c0.74-0.12,2.38-0.13,3.49,0.15"/>
		<path id="kvg:09475-s5" kvg:type="㇑a" d="M28.45,41.19c1.24,0.78,1.24,2.52,1.24,3.14c0,4.35,0.12,35.13-0.19,39.48"/>
		<path id="kvg:09475-s6" kvg:type="㇔" d="M15.14,65.51c3.63,5.13,5.41,10.69,6.15,13.69"/>
		<path id="kvg:09475-s7" kvg:type="㇒" d="M41.24,60.05c0.27,0.85,0.41,2.09,0.3,2.82c-0.27,1.85-2.38,5.23-5.38,10.66"/>
		<path id="kvg:09475-s8" kvg:type="㇀/㇐" d="M12,89.33c1.29,0.88,2.85,0.66,4.66,0.22c1.34-0.32,11.64-5.04,25.09-10.3"/>
	</g>
	<g id="kvg:09475-g2" kvg:position="right">
		<g id="kvg:09475-g3" kvg:element="艹" kvg:variant="true" kvg:original="艸" kvg:position="top">
			<path id="kvg:09475-s9" kvg:type="㇐" d="M46.16,22.57C47.33,23,48.7,23.08,49.88,23c10.81-0.78,30.54-4.31,43-4.37c1.96-0.01,3.14,0.2,4.12,0.41"/>
			<path id="kvg:09475-s10" kvg:type="㇑a" d="M59.91,12.7c0.95,0.86,1.25,1.12,1.36,1.65c0.95,4.96,0.76,11.07,1.03,12.87"/>
			<path id="kvg:09475-s11" kvg:type="㇑a" d="M79.98,10.28c0.17,0.85,0.39,1.48,0.2,2.52c-1.1,6.11-1.1,8.17-2.29,13.93"/>
		</g>
		<g id="kvg:09475-g4" kvg:position="bottom">
			<g id="kvg:09475-g5" kvg:element="口">
				<path id="kvg:09475-s12" kvg:type="㇑" d="M49.26,33.47c0.26,0.22,0.52,0.4,0.64,0.68c0.9,2.18,1.97,8.19,2.59,12.1"/>
				<path id="kvg:09475-s13" kvg:type="㇕b" d="M50.87,34.44c5.3-1.14,11.76-1.93,14.79-2.21c1.11-0.11,1.78,0.62,1.62,1.23c-0.78,3.02-1.55,6.12-2.4,9.18"/>
				<path id="kvg:09475-s14" kvg:type="㇐b" d="M52.61,44.34c3.57-0.32,8.52-0.85,13.42-1.35"/>
			</g>
			<g id="kvg:09475-g6" kvg:element="口">
				<path id="kvg:09475-s15" kvg:type="㇑" d="M72.85,30.9c0.28,0.25,0.56,0.46,0.68,0.77c0.96,2.48,2.1,7.8,2.76,12.26"/>
				<path id="kvg:09475-s16" kvg:type="㇕b" d="M74.57,32.58c5.64-1.29,12.51-2.21,15.74-2.54c1.18-0.12,1.89,0.71,1.72,1.41c-0.7,2.9-1.52,4.86-2.56,8.87"/>
				<path id="kvg:09475-s17" kvg:type="㇐b" d="M76.41,42.26c3.8-0.37,9.03-1.01,14.24-1.57"/>
			</g>
			<g id="kvg:09475-g7" kvg:element="隹">
				<g id="kvg:09475-g8" kvg:element="亻" kvg:variant="true" kvg:original="人">
					<path id="kvg:09475-s18" kvg:type="㇒" d="M56.58,50.5c0.13,0.96-0.04,2.21-0.46,3.02c-2.71,5.22-6.15,9.63-12.37,15.98"/>
					<path id="kvg:09475-s19" kvg:type="㇑" d="M52.83,58.71c0.59,0.56,1.04,1.8,1.08,2.67c0.35,7.78-0.48,32.82-0.19,36.87"/>
				</g>
				<path id="kvg:09475-s20" kvg:type="㇒" d="M75.42,47.75c0.05,0.47-0.01,1.09-0.17,1.49c-0.96,2.56-2.18,4.54-4.38,7.67"/>
				<path id="kvg:09475-s21" kvg:type="㇐b" d="M53.75,59.26c6.83-0.76,37.34-2.96,40.27-3.38"/>
				<path id="kvg:09475-s22" kvg:type="㇑a" d="M72.89,59.71c0.33,0.3,0.59,0.7,0.59,1.22c0,5.22,0.04,22.39-0.16,30.6"/>
				<path id="kvg:09475-s23" kvg:type="㇐b" d="M54.66,70.67c6.45-0.63,34.29-2.97,37.05-3.31"/>
				<path id="kvg:09475-s24" kvg:type="㇐b" d="M54.44,81.27c6.8-0.53,34.77-2.62,37.69-2.91"/>
				<path id="kvg:09475-s25" kvg:type="㇐b" d="M53.88,93.28c6.83-0.76,41.62-2.24,44.56-2.65"/>
			</g>
		</g>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_09475" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 22.50 12.50)">1</text>
	<text transform="matrix(1 0 0 1 35.50 20.50)">2</text>
	<text transform="matrix(1 0 0 1 23.50 38.50)">3</text>
	<text transform="matrix(1 0 0 1 6.50 56.85)">4</text>
	<text transform="matrix(1 0 0 1 22.50 49.50)">5</text>
	<text transform="matrix(1 0 0 1 8.50 66.85)">6</text>
	<text transform="matrix(1 0 0 1 33.63 63.85)">7</text>
	<text transform="matrix(1 0 0 1 4.50 91.50)">8</text>
	<text transform="matrix(1 0 0 1 44.50 19.50)">9</text>
	<text transform="matrix(1 0 0 1 52.50 10.50)">10</text>
	<text transform="matrix(1 0 0 1 71.50 7.50)">11</text>
	<text transform="matrix(1 0 0 1 42.50 40.50)">12</text>
	<text transform="matrix(1 0 0 1 51.50 31.50)">13</text>
	<text transform="matrix(1 0 0 1 53.50 41.50)">14</text>
	<text transform="matrix(1 0 0 1 67.50 41.50)">15</text>
	<text transform="matrix(1 0 0 1 80.50 28.50)">16</text>
	<text transform="matrix(1 0 0 1 77.50 39.50)">17</text>
	<text transform="matrix(1 0 0 1 45.50 53.50)">18</text>
	<text transform="matrix(1 0 0 1 42.25 75.28)">19</text>
	<text transform="matrix(1 0 0 1 65.25 49.20)">20</text>
	<text transform="matrix(1 0 0 1 60.50 56.50)">21</text>
	<text transform="matrix(1 0 0 1 76.50 65.13)">22</text>
	<text transform="matrix(1 0 0 1 57.50 67.50)">23</text>
	<text transform="matrix(1 0 0 1 57.50 78.13)">24</text>
	<text transform="matrix(1 0 0 1 57.50 90.13)">25</text>
</g>
</svg>
