<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2009/2010/2011 <PERSON>.
This work is distributed under the conditions of the Creative Commons
Attribution-Share Alike 3.0 Licence. This means you are free:
* to Share - to copy, distribute and transmit the work
* to Remix - to adapt the work

Under the following conditions:
* Attribution. You must attribute the work by stating your use of KanjiVG in
  your own copyright header and linking to KanjiVG's website
  (http://kanjivg.tagaini.net)
* Share Alike. If you alter, transform, or build upon this work, you may
  distribute the resulting work only under the same or similar license to this
  one.

See http://creativecommons.org/licenses/by-sa/3.0/ for more details.
-->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST g
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:element CDATA #IMPLIED
kvg:variant CDATA #IMPLIED
kvg:partial CDATA #IMPLIED
kvg:original CDATA #IMPLIED
kvg:part CDATA #IMPLIED
kvg:number CDATA #IMPLIED
kvg:tradForm CDATA #IMPLIED
kvg:radicalForm CDATA #IMPLIED
kvg:position CDATA #IMPLIED
kvg:radical CDATA #IMPLIED
kvg:phon CDATA #IMPLIED >
<!ATTLIST path
xmlns:kvg CDATA #FIXED "http://kanjivg.tagaini.net"
kvg:type CDATA #IMPLIED >
]>
<svg xmlns="http://www.w3.org/2000/svg" width="109" height="109" viewBox="0 0 109 109">
<g id="kvg:StrokePaths_0990c" style="fill:none;stroke:#000000;stroke-width:3;stroke-linecap:round;stroke-linejoin:round;">
<g id="kvg:0990c" kvg:element="餌">
	<g id="kvg:0990c-g1" kvg:element="⻞" kvg:original="食" kvg:position="left" kvg:radical="general">
		<path id="kvg:0990c-s1" kvg:type="㇒" d="M34.74,12.14c0.07,0.74,0.33,1.97-0.13,2.97c-3,6.52-11.63,19.29-23.86,28.89"/>
		<path id="kvg:0990c-s2" kvg:type="㇔/㇏" d="M35.52,17.58c5.2,2.5,13.43,10.28,14.73,14.17"/>
		<path id="kvg:0990c-s3" kvg:type="㇐" d="M29.28,32.16c0.39,0.15,1.12,0.19,1.51,0.15c2.51-0.25,9.11-2.17,11.54-2c0.65,0.05,1.05,0.07,1.38,0.14"/>
		<path id="kvg:0990c-s4" kvg:type="㇑" d="M22.77,41.44c0.4,0.78,0.81,1.66,0.81,2.7c0,1.04-0.13,54.33,0,55.37"/>
		<path id="kvg:0990c-s5" kvg:type="㇕" d="M23.72,43.05c2.27-0.13,18.45-2.15,20.52-2.31c1.72-0.13,2.83,1.44,2.7,2.2c-0.27,1.56-1.98,17.89-2.54,21.33"/>
		<path id="kvg:0990c-s6" kvg:type="㇐a" d="M24.12,53.12c3.07,0,17.88-1.79,21.36-1.79"/>
		<path id="kvg:0990c-s7" kvg:type="㇐a" d="M23.98,64.36c6.26-0.56,12.71-1.54,20.51-2.02"/>
		<path id="kvg:0990c-s8" kvg:type="㇐c" d="M23.92,76.75c3.85-0.25,16.49-2.64,20.22-2.47c1,0.04,1.61,0.07,2.11,0.14"/>
		<path id="kvg:0990c-s9" kvg:type="㇐c" d="M24.44,89.88c3.85-0.25,17-2.15,20.73-1.98c1,0.04,1.61,0.07,2.11,0.14"/>
	</g>
	<g id="kvg:0990c-g2" kvg:element="耳" kvg:position="right">
		<path id="kvg:0990c-s10" kvg:type="㇐" d="M52.6,25.92c0.91,0.46,2.58,0.53,3.5,0.46c10.06-0.69,18.28-3,36.64-4.38c1.52-0.11,2.44,0.22,3.2,0.45"/>
		<path id="kvg:0990c-s11" kvg:type="㇑a" d="M60.1,28.93c0.79,0.52,1.22,1.89,1.22,3.29s-0.24,41.94-0.24,47"/>
		<path id="kvg:0990c-s12" kvg:type="㇐a" d="M61.65,43.82c4.97-0.69,14.9-1.77,22.22-2.23"/>
		<path id="kvg:0990c-s13" kvg:type="㇐a" d="M61.27,61.9c6.04-0.92,14.58-2.54,22.5-3"/>
		<path id="kvg:0990c-s14" kvg:type="㇀" d="M52.31,79.68c0.44,1.07,2.44,1.57,4.36,1.08c4.66-1.21,29.96-7.82,37.13-9.73"/>
		<path id="kvg:0990c-s15" kvg:type="㇑" d="M83.03,25.69c0.37,0.85,0.93,1.92,0.93,2.98c0,1.06,0.19,63.1,0.19,71.83"/>
	</g>
</g>
</g>
<g id="kvg:StrokeNumbers_0990c" style="font-size:8;fill:#808080">
	<text transform="matrix(1 0 0 1 26.50 13.50)">1</text>
	<text transform="matrix(1 0 0 1 41.75 17.05)">2</text>
	<text transform="matrix(1 0 0 1 32.50 29.50)">3</text>
	<text transform="matrix(1 0 0 1 17.25 49.75)">4</text>
	<text transform="matrix(1 0 0 1 25.50 39.50)">5</text>
	<text transform="matrix(1 0 0 1 27.48 49.75)">6</text>
	<text transform="matrix(1 0 0 1 27.53 61.50)">7</text>
	<text transform="matrix(1 0 0 1 27.50 73.75)">8</text>
	<text transform="matrix(1 0 0 1 27.49 86.25)">9</text>
	<text transform="matrix(1 0 0 1 51.50 22.50)">10</text>
	<text transform="matrix(1 0 0 1 52.50 34.50)">11</text>
	<text transform="matrix(1 0 0 1 64.50 40.50)">12</text>
	<text transform="matrix(1 0 0 1 64.50 57.50)">13</text>
	<text transform="matrix(1 0 0 1 49.50 75.50)">14</text>
	<text transform="matrix(1 0 0 1 73.50 33.50)">15</text>
</g>
</svg>
